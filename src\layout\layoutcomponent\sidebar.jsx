import React, { Fragment, useState, useEffect, useContext } from 'react';
import { MENUITEMS } from 'common/sidemenu';
import PerfectScrollbar from 'react-perfect-scrollbar';
import { NavLink } from 'react-router-dom';
import { useLocation } from 'react-router-dom';
import { imagesData } from 'common/commonimages';
import AuthContext from 'context/auth-context.jsx';
let history = [];

const Sidebar = () => {
  let location = useLocation();
  const { profile } = useContext(AuthContext);

  // Filter menu items based on user's ministerial status
  const getFilteredMenuItems = () => {
    if (!profile) return MENUITEMS;

    return MENUITEMS.map((menuGroup) => ({
      ...menuGroup,
      Items: menuGroup.Items.filter((item) => {
        // Define ministerial-only routes (removed chart-reports from here)
        const ministerialRoutes = ['/app/users', '/app/institute'];

        // If user is not ministerial and this is a ministerial route, hide it
        if (!profile.is_ministerial && ministerialRoutes.includes(item.path)) {
          return false;
        }

        return true;
      }),
    }));
  };

  const [menuitems, setMenuitems] = useState(getFilteredMenuItems());

  // Update menu items when profile changes
  useEffect(() => {
    setMenuitems(getFilteredMenuItems());
  }, [profile]);

  // initial loading
  useEffect(() => {
    history.push(location.pathname); // add  history to history  stack for current location.pathname to prevent multiple history calls innerWidth  and innerWidth  calls from  multiple users. This is important because the history stack is not always empty when the user clicks  the history
    if (history.length > 2) {
      history.shift();
    }
    if (history[0] !== history[1]) {
      setSidemenu();
    }
  }, [location]);

  //<-------End---->
  function setSidemenu() {
    if (menuitems) {
      menuitems.filter((mainlevel) => {
        if (mainlevel.Items) {
          mainlevel.Items.filter((items) => {
            items.active = false;
            items.selected = false;

            if (location.pathname === items.path) {
              items.active = true;
              items.selected = true;
            }
            if (items.children) {
              items.children.filter((submenu) => {
                submenu.active = false;
                submenu.selected = false;
                if (location.pathname === submenu.path) {
                  items.active = true;
                  items.selected = true;
                  submenu.active = true;
                  submenu.selected = true;
                }
                if (submenu.children) {
                  submenu.children.filter((submenu1) => {
                    submenu1.active = false;
                    submenu1.selected = false;
                    if (location.pathname === submenu1.path) {
                      items.active = true;
                      items.selected = true;
                      submenu.active = true;
                      submenu.selected = true;
                      submenu1.active = true;
                      submenu1.selected = true;
                    }
                    return submenu1;
                  });
                }
                return submenu;
              });
            }
            return items;
          });
        }
        setMenuitems((arr) => [...arr]);
        return mainlevel;
      });
    }
  }
  function toggleSidemenu(item) {
    if (!document.body.classList.contains('horizontalmenu-hover') || window.innerWidth < 992) {
      // To show/hide the menu
      if (!item.active) {
        menuitems.filter((mainlevel) => {
          if (mainlevel.Items) {
            mainlevel.Items.filter((sublevel) => {
              sublevel.active = false;
              if (item === sublevel) {
                sublevel.active = true;
              }
              if (sublevel.children) {
                sublevel.children.filter((sublevel1) => {
                  sublevel1.active = false;
                  if (item === sublevel1) {
                    sublevel.active = true;
                    sublevel1.active = true;
                  }
                  if (sublevel1.children) {
                    sublevel1.children.filter((sublevel2) => {
                      sublevel2.active = false;
                      if (item === sublevel2) {
                        sublevel.active = true;
                        sublevel1.active = true;
                        sublevel2.active = true;
                      }
                      if (sublevel2.children) {
                        sublevel2.children.filter((sublevel3) => {
                          sublevel3.active = false;
                          if (item === sublevel3) {
                            sublevel.active = true;
                            sublevel1.active = true;
                            sublevel2.active = true;
                            sublevel3.active = true;
                          }
                          return sublevel2;
                        });
                      }
                      return sublevel2;
                    });
                  }
                  return sublevel1;
                });
              }
              return sublevel;
            });
          }
          return mainlevel;
        });
      } else {
        item.active = !item.active;
      }
    }

    setMenuitems((arr) => [...arr]);
  }

  //Hover effect
  function Onhover() {
    if (document.querySelector('.app')) {
      if (document.querySelector('.app').classList.contains('sidenav-toggled'))
        document.querySelector('.app').classList.add('sidenav-toggled-open');
    }
  }
  function Outhover() {
    if (document.querySelector('.app')) {
      document.querySelector('.app').classList.remove('sidenav-toggled-open');
    }
  }

  return (
    <div className="sticky">
      <aside className="app-sidebar " onMouseOver={() => Onhover()} onMouseOut={() => Outhover()}>
        {/* <Scrollbars
          options={{ suppressScrollX: true }}
          className="hor-scroll"
          style={{ position: "absolute" }}
        > */}
        <PerfectScrollbar
          options={{ suppressScrollX: true }}
          className="hor-scroll"
          // style={{ position: "absolute" }}
        >
          <div className="main-sidebar-header active">
            <NavLink className="header-logo active" to={`/app/dashboard/`}>
              <img src={imagesData('logosample')} className="main-logo  desktop-logo" alt="logo" />
              {/*<img*/}
              {/*  src={imagesData('logo')}*/}
              {/*  className="main-logo  desktop-dark"*/}
              {/*  alt="logo"*/}
              {/*/>*/}
              <img
                src={imagesData('logosingle')}
                className="main-logo  mobile-logo"
                style={{ maxHeight: '40px' }}
                alt="logo"
              />
              {/*<img*/}
              {/*  src={imagesData('logo')}*/}
              {/*  className="main-logo  mobile-dark"*/}
              {/*  alt="logo"*/}
              {/*/>*/}
            </NavLink>
          </div>
          <div className="main-sidemenu">
            <div className="slide-left disabled" id="slide-left">
              <svg xmlns="http://www.w3.org/2000/svg" fill="#7b8191" width="24" height="24" viewBox="0 0 24 24">
                <path d="M13.293 6.293 7.586 12l5.707 5.707 1.414-1.414L10.414 12l4.293-4.293z" />
              </svg>
            </div>

            <ul className="side-menu" style={{ marginTop: 120 }}>
              {menuitems.map((Item, itemi) => (
                <Fragment key={itemi + Math.random() * 100}>
                  {/*<li className="side-item side-item-category">{Item.menutitle}</li>*/}
                  {Item.Items.map((menuItem, i) => (
                    <li
                      className={`slide mb-2 ${menuItem.selected ? 'is-expanded' : ''}  ${menuItem.active ? 'is-expanded' : ''}`}
                      key={i}
                    >
                      {menuItem.type === 'link' ? (
                        <NavLink
                          to={menuItem.path + '/'}
                          className={`side-menu__item ${menuItem.selected ? ' active' : ''}`}
                        >
                          {menuItem.icon}
                          <span className="side-menu__label">{menuItem.title}</span>
                          {menuItem.badge ? <label className={menuItem.badge}>{menuItem.badgetxt}</label> : ''}
                        </NavLink>
                      ) : (
                        ''
                      )}
                      {menuItem.type === 'sub' ? (
                        <a
                          href="javascript"
                          onClick={(event) => {
                            event.preventDefault();
                            toggleSidemenu(menuItem);
                          }}
                          className={`side-menu__item ${menuItem.selected ? 'active is-expanded' : ''}`}
                        >
                          {menuItem.icon}
                          <span className="side-menu__label">
                            {menuItem.title}
                            {menuItem.active}
                          </span>
                          {menuItem.badge ? (
                            <label className={`${menuItem.badge} side-badge`}>{menuItem.badgetxt}</label>
                          ) : (
                            ''
                          )}
                          <i className="angle fe fe-chevron-right"></i>
                        </a>
                      ) : (
                        ''
                      )}

                      {menuItem.children ? (
                        <ul
                          className={`slide-menu ${menuItem.active ? 'open' : ''}`}
                          style={menuItem.active ? { display: 'block' } : { display: 'none' }}
                        >
                          {menuItem.children.map((childrenItem, index) => {
                            return (
                              <li
                                key={index}
                                className={`sub-slide ${childrenItem.selected ? 'is-expanded' : ''} ${childrenItem.active ? 'is-expanded' : ''}`}
                              >
                                {childrenItem.type === 'sub' ? (
                                  <a
                                    href="javascript"
                                    className={`slide-item ${childrenItem.selected ? 'active is-expanded' : ''}`}
                                    onClick={(event) => {
                                      event.preventDefault();
                                      toggleSidemenu(childrenItem);
                                    }}
                                  >
                                    <span className="sub-side-menu__label">
                                      {childrenItem.title}
                                      {childrenItem.active}
                                    </span>

                                    <i className="sub-angle fe fe-chevron-right"></i>
                                  </a>
                                ) : (
                                  ''
                                )}
                                {childrenItem.type === 'link' ? (
                                  <span as="li">
                                    <NavLink to={childrenItem.path + '/'} className="slide-item">
                                      {childrenItem.title}
                                      {childrenItem.active}
                                    </NavLink>
                                  </span>
                                ) : (
                                  ''
                                )}
                                {childrenItem.children ? (
                                  <ul
                                    className={`sub-slide-menu ${childrenItem.selected ? 'open' : ''}`}
                                    style={childrenItem.active ? { display: 'block' } : { display: 'none' }}
                                  >
                                    {childrenItem.children.map((childrenSubItem, key) => (
                                      <li key={key}>
                                        {childrenSubItem.type === 'link' ? (
                                          <NavLink to={childrenSubItem.path + '/'} className="sub-side-menu__item">
                                            <span className="sub-side-menu__label">
                                              {childrenSubItem.title}
                                              {childrenSubItem.active}
                                            </span>
                                          </NavLink>
                                        ) : (
                                          ''
                                        )}
                                        {childrenSubItem.type === 'sub' ? (
                                          <span
                                            as="li"
                                            className={`sub-slide2 ${childrenSubItem.selected ? 'is-expanded' : ''} ${childrenSubItem.active ? 'is-expanded' : ''}`}
                                          >
                                            <NavLink
                                              to="#"
                                              className="sub-side-menu__item"
                                              onClick={(event) => {
                                                event.preventDefault();
                                                toggleSidemenu(childrenSubItem);
                                              }}
                                            >
                                              <span className="sub-side-menu__label">
                                                {childrenSubItem.title}
                                                {childrenSubItem.active}
                                              </span>
                                              <i className="sub-angle2 fe fe-chevron-down"></i>
                                            </NavLink>
                                            {childrenItem.children.map((childrenSubItemsub, key) => (
                                              <ul
                                                key={key}
                                                className={`sub-slide-menu1 ${childrenSubItemsub.selected ? 'open' : ''}`}
                                                style={
                                                  childrenSubItemsub.active ? { display: 'block' } : { display: 'none' }
                                                }
                                              >
                                                {childrenItem.children.map((childrenSubItemsubs, key) => (
                                                  <li key={key}>
                                                    <NavLink className="sub-slide-item2" to="#">
                                                      {childrenSubItemsubs.title}
                                                      {childrenSubItemsubs.active}
                                                    </NavLink>
                                                  </li>
                                                ))}
                                              </ul>
                                            ))}
                                          </span>
                                        ) : (
                                          ''
                                        )}
                                      </li>
                                    ))}
                                  </ul>
                                ) : (
                                  ''
                                )}
                              </li>
                            );
                          })}
                        </ul>
                      ) : (
                        ''
                      )}
                    </li>
                  ))}
                  {/*<li className={`slide`} key={999}>*/}
                  {/*  <a to={'#'} className={`side-menu__item`}>*/}
                  {/*    <svg*/}
                  {/*      className="side-menu__icon"*/}
                  {/*      width="18"*/}
                  {/*      height="19"*/}
                  {/*      viewBox="0 0 18 19"*/}
                  {/*      fill="none"*/}
                  {/*      xmlns="http://www.w3.org/2000/svg"*/}
                  {/*    >*/}
                  {/*      <path d="M9 2.1391C7.55373 2.1391 6.13993 2.56797 4.9374 3.37148C3.73486 4.17499 2.7976 5.31704 2.24413 6.65323C1.69067 7.98941 1.54586 9.45971 1.82801 10.8782C2.11017 12.2967 2.80661 13.5996 3.82928 14.6223C4.85196 15.645 6.15492 16.3414 7.57341 16.6236C8.99189 16.9057 10.4622 16.7609 11.7984 16.2075C13.1346 15.654 14.2766 14.7167 15.0801 13.5142C15.8836 12.3117 16.3125 10.8979 16.3125 9.4516C16.3105 7.51283 15.5394 5.65406 14.1685 4.28314C12.7975 2.91223 10.9388 2.14115 9 2.1391ZM14.0534 5.88465L9.5625 8.47707V3.29011C10.4542 3.37187 11.3175 3.64619 12.0929 4.09414C12.8682 4.54208 13.5371 5.15298 14.0534 5.88465ZM8.4375 3.29011V9.12605L3.38274 12.044C2.96778 11.1446 2.77482 10.1587 2.82006 9.16925C2.86531 8.17977 3.14745 7.21561 3.64276 6.35782C4.13806 5.50004 4.83207 4.77371 5.66643 4.23988C6.50078 3.70605 7.4511 3.38034 8.4375 3.29011ZM9 15.6391C8.00943 15.6387 7.03344 15.4005 6.15407 14.9445C5.27471 14.4884 4.51769 13.828 3.94664 13.0186L14.6173 6.85847C15.0524 7.80119 15.2433 8.83825 15.1724 9.87413C15.1015 10.91 14.7711 11.9114 14.2115 12.786C13.652 13.6606 12.8814 14.3804 11.9706 14.879C11.0599 15.3776 10.0383 15.639 9 15.6391Z" />*/}
                  {/*    </svg>*/}
                  {/*    <span className="side-menu__label">گزارش‌های مدیریتی</span>*/}
                  {/*  </a>*/}
                  {/*</li>*/}
                </Fragment>
              ))}
            </ul>
            <div className="slide-right" id="slide-right">
              <svg xmlns="http://www.w3.org/2000/svg" fill="#7b8191" width="24" height="24" viewBox="0 0 24 24">
                <path d="M10.707 17.707 16.414 12l-5.707-5.707-1.414 1.414L13.586 12l-4.293 4.293z" />
              </svg>
            </div>
          </div>
          {/* </Scrollbars> */}
        </PerfectScrollbar>
      </aside>
    </div>
  );
};

Sidebar.propTypes = {};
export default Sidebar;
