import { create } from 'zustand';

const oneYearInMillis = 12 * 30 * 24 * 60 * 60 * 1000;

const initialState = {
  filter: {
    source: 'all',
    q: '',
    national_id: '',
    cert_num: '',
    father_name: '',
    // birth_date_from: new Date(new Date().getTime() - oneYearInMillis),
    // birth_date_from: '',
    // birth_date_to: '',
    // birth_date_to: new Date(),
    gender: 'all',
    person: {
      first_name: '',
      last_name: '',
      issue_place: '',
      // issue_data_from: '',
      // issue_data_to: '',
      birth_place: '',
      province: '',
      county: '',
      marriage: '',
      // marriage_date_from: '',
      // marriage_date_to: '',
      spouse_national_id: '',
      address: '',
      district: '',
      child_count: 0,
      // child_name: [],
      // child_nation_id: [],
      mobile: '',
      phone: '',
      postal_code: '',
    },
    passport: {
      first_name: '',
      last_name: '',
      // issue_data_from: '',
      // issue_data_to: '',
      address: '',
      birth_place: '',
      passport_num: '',
      passport_status: '',
      // expiry_date_from: '',
      // expiry_date_to: '',
      postal_code: '',
      mobile: '',
    },

    vehicle: {
      first_name: '',
      last_name: '',
      // issue_data_from: '',
      // issue_data_to: '',
      // address: '',
      // birth_place: '',
      // passport_num: '',
      // passport_status: '',
      // expiry_date_from: '',
      // expiry_date_to: '',
      postal_code: '',
      mobile: '',
    },
    // vehicleData: {
    //   plate_num: '',
    //   ownership_code: '',
    //   type: '',
    //   system: '',
    //   tip: '',
    //   model: '',
    //   color: '',
    //   capacity: '',
    //   cylinder_num: '',
    //   fuel_type: '',
    //   axel_num: '',
    //   wheel_num: '',
    //   engine_num: '',
    //   chassis_num: '',
    //   cylinder_vol: '',
    //   vin: '',
    //   owner_name: '',
    //   owner_national_id: '',
    //   owner_cert_num: '',
    //   buyer_name: '',
    //   buyer_national_id: '',
    //   buyer_cert_num: '',
    //   plate_current_state: '',
    //   numbering_place: '',
    //   numbering_unit_code: '',
    //   numbering_unit_head: '',
    //   plate_allocation_user: '',
    //   doc_check_user: '',
    //   plate_redeem_place: '',
    //   confirm_date: '',
    // },
    page: 1,
    page_size: 12,
  },
};

// Create Zustand store
const useSearchStore = create((set, get) => ({
  ...initialState,
  setFilter: (newFilter) => set((state) => ({ filter: { ...state.filter, ...newFilter } })),
  removeFilter: (rest) => set((state) => ({ filter: { ...rest } })),
  clearFilter: () => {
    set({ filter: initialState.filter });
  },
}));

export default useSearchStore;
