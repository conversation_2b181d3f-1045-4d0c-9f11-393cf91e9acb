import React, { Fragment, useState, useEffect } from 'react';
import { Form, FormGroup, Button } from 'react-bootstrap';
import Select from 'react-select';
import useSubmitDocumentFormStore from 'store/submitDocumentFormStore.js';
import InstituteService from 'service/api/instituteService.js';
import toastService from 'utils/toastService.js';
import { FORM_OPTIONS } from 'utils/formOptions.js';
import { validateNationalId, formatNationalIdInput, validatePhoneNumber, formatPhoneInput } from 'utils/helper.js';

const Step3 = ({ validationErrors }) => {
  const { step3, setStep3 } = useSubmitDocumentFormStore();

  // State for institutes list and loading
  const [institutes, setInstitutes] = useState([]);
  const [loading, setLoading] = useState(false);

  // Fetch institutes on component mount
  useEffect(() => {
    const fetchInstitutes = async () => {
      try {
        setLoading(true);
        const response = await InstituteService.getMyInstitute({ page_size: 1000 });
        const instituteData = response?.data?.data?.institutes || [];

        // Transform the data to match Select component format
        const instituteOptions = [
          { value: '', label: 'انتخاب کنید' },
          ...instituteData.map((institute) => ({
            value: institute.uuid,
            label: institute.title,
          })),
          { value: 'other', label: 'سایر' },
        ];

        setInstitutes(instituteOptions);
      } catch (error) {
        console.error('Error fetching institutes:', error);
        toastService.error('خطا در دریافت لیست موسسات');
        // Fallback to default options
        setInstitutes([
          { value: '', label: 'انتخاب کنید' },
          { value: 'other', label: 'سایر' },
        ]);
      } finally {
        setLoading(false);
      }
    };

    fetchInstitutes();
  }, []);

  // Helper function to handle validation errors for institute fields
  const getInstituteError = (field, index) => {
    const errorKey = `institute_${field}_${index}`;
    return validationErrors[errorKey] ? (
      <div className="invalid-feedback" style={{ display: 'block' }}>
        {validationErrors[errorKey]}
      </div>
    ) : null;
  };

  return (
    <section className="card-body Basicwizard">
      {/* مشخصات */}
      <h5>مشخصات</h5>
      <Form.Group className="control-group form-group">
        <Form.Label className="form-label">نام و نام خانوادگی</Form.Label>
        <Form.Control
          id="reported-fullname"
          type="text"
          size="sm"
          className="form-control"
          required
          placeholder="نام و نام خانوادگی را وارد کنید"
          value={step3.reportedFullname || ''}
          onChange={(e) => setStep3({ reportedFullname: e.target.value })}
        />
        <div className="invalid-feedback" style={{ display: validationErrors.reportedFullname ? 'block' : 'none' }}>
          {validationErrors.reportedFullname}
        </div>
      </Form.Group>

      <Form.Group className="form-group">
        <Form.Label className="form-label">جنسیت</Form.Label>
        <div className="d-flex flex-row align-items-center gap-4">
          <Form.Label className="custom-control custom-radio">
            <Form.Control
              type="radio"
              className="custom-control-input"
              name="reported-gender"
              value="male"
              checked={step3.reportedGender === 'male'}
              onChange={() => setStep3({ reportedGender: 'male' })}
            />
            <span className="custom-control-label">مرد</span>
          </Form.Label>
          <Form.Label className="custom-control custom-radio">
            <Form.Control
              type="radio"
              className="custom-control-input"
              name="reported-gender"
              value="female"
              checked={step3.reportedGender === 'female'}
              onChange={() => setStep3({ reportedGender: 'female' })}
            />
            <span className="custom-control-label">زن</span>
          </Form.Label>
        </div>
        <div className="invalid-feedback" style={{ display: validationErrors.reportedGender ? 'block' : 'none' }}>
          {validationErrors.reportedGender}
        </div>
      </Form.Group>

      <Form.Group className="form-group">
        <Form.Label htmlFor="reported-nationalID">کد ملی</Form.Label>
        <Form.Control
          id="reported-nationalID"
          type="text"
          size="sm"
          className={`form-control ${
            step3.reportedNationalID && !validateNationalId(step3.reportedNationalID) ? 'is-invalid' : ''
          }`}
          placeholder="کد ملی را وارد کنید (۱۰ رقم)"
          value={step3.reportedNationalID || ''}
          onChange={(e) => {
            const formattedValue = formatNationalIdInput(e.target.value);
            setStep3({ reportedNationalID: formattedValue });
          }}
          maxLength={10}
        />
        <div
          className="invalid-feedback"
          style={{
            display:
              validationErrors.reportedNationalID ||
              (step3.reportedNationalID && !validateNationalId(step3.reportedNationalID))
                ? 'block'
                : 'none',
          }}
        >
          {validationErrors.reportedNationalID ||
            (step3.reportedNationalID && !validateNationalId(step3.reportedNationalID)
              ? 'کد ملی وارد شده معتبر نیست'
              : '')}
        </div>
      </Form.Group>

      <Form.Group className="form-group">
        <Form.Label>شماره تماس</Form.Label>
        <Form.Control
          id="reported-phone"
          type="text"
          size="sm"
          className={`form-control ${
            step3.reportedPhone && !validatePhoneNumber(step3.reportedPhone) ? 'is-invalid' : ''
          }`}
          placeholder="شماره تماس را وارد کنید (موبایل: ۱۱ رقم، ثابت: ۸-۱۱ رقم)"
          value={step3.reportedPhone || ''}
          onChange={(e) => {
            const formattedValue = formatPhoneInput(e.target.value);
            setStep3({ reportedPhone: formattedValue });
          }}
          maxLength={11}
        />
        <div
          className="invalid-feedback"
          style={{
            display:
              validationErrors.reportedPhone || (step3.reportedPhone && !validatePhoneNumber(step3.reportedPhone))
                ? 'block'
                : 'none',
          }}
        >
          {validationErrors.reportedPhone ||
            (step3.reportedPhone && !validatePhoneNumber(step3.reportedPhone) ? 'شماره تماس وارد شده معتبر نیست' : '')}
        </div>
      </Form.Group>

      <Form.Group className="form-group">
        <Form.Label>رایانامه</Form.Label>
        <Form.Control
          id="reported-email"
          type="text"
          size="sm"
          className="form-control"
          placeholder="رایانامه خود را وارد کنید"
          value={step3.reportedEmail || ''}
          onChange={(e) => setStep3({ reportedEmail: e.target.value })}
        />
        <div className="invalid-feedback" style={{ display: validationErrors.reportedEmail ? 'block' : 'none' }}>
          {validationErrors.reportedEmail}
        </div>
      </Form.Group>

      <Form.Group className="control-group form-group">
        <Form.Label className="form-label">موسسه محل خدمت</Form.Label>
        <Select
          options={institutes}
          placeholder={loading ? 'در حال بارگذاری...' : 'موسسه خود را انتخاب کنید'}
          classNamePrefix="Select2"
          isSearchable
          isLoading={loading}
          isDisabled={loading}
          value={institutes.find((option) => option.value === step3.reportedOrgan) || null}
          onChange={(e) =>
            setStep3({
              reportedOrgan: e?.value || '',
              reportedCustomOrgan: e?.value === 'other' ? '' : step3.reportedCustomOrgan,
            })
          }
        />
        <div className="invalid-feedback" style={{ display: validationErrors.reportedOrgan ? 'block' : 'none' }}>
          {validationErrors.reportedOrgan}
        </div>
      </Form.Group>
      {step3.reportedOrgan === 'other' && (
        <Form.Group className="control-group form-group">
          <Form.Label className="form-label">نام موسسه</Form.Label>
          <Form.Control
            id="reported-organ-name"
            type="text"
            className="form-control"
            required
            placeholder="نام موسسه"
            value={step3.reportedCustomOrgan || ''}
            onChange={(e) => setStep3({ reportedCustomOrgan: e.target.value })}
          />

          <div
            className="invalid-feedback"
            style={{ display: validationErrors.reportedCustomOrgan ? 'block' : 'none' }}
          >
            {validationErrors.reportedCustomOrgan}
          </div>
        </Form.Group>
      )}

      <Form.Group className="control-group form-group">
        <Form.Label className="form-label">نقش حرفه‌ای و آکادمیک</Form.Label>
        <Select
          options={FORM_OPTIONS.roleType}
          placeholder="نقش آکادمیک خود را انتخاب کنید"
          classNamePrefix="Select2"
          isSearchable
          value={FORM_OPTIONS.roleType.find((option) => option.value === step3.reportedRole)}
          onChange={(e) =>
            setStep3({
              reportedRole: e.value,
              reportedGrade: '',
              reportedField: '',
              reportedYearsOfService: '',
              reportedEducationLevel: '',
              reportedAcademicStatus: '',
              reportedAdministrativePosition: '',
            })
          }
        />

        <div className="invalid-feedback" style={{ display: validationErrors.reportedRole ? 'block' : 'none' }}>
          {validationErrors.reportedRole}
        </div>
      </Form.Group>

      {step3.reportedRole === 'faculty_member' && (
        <Fragment>
          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">مرتبه علمی</Form.Label>
            <Form.Control
              id="reported-faculty-grade"
              type="text"
              className="form-control"
              required
              placeholder="مرتبه علمی خود را وارد کنید"
              value={step3.reportedGrade || ''}
              onChange={(e) => setStep3({ reportedGrade: e.target.value })}
            />

            <div className="invalid-feedback" style={{ display: validationErrors.reportedGrade ? 'block' : 'none' }}>
              {validationErrors.reportedGrade}
            </div>
          </Form.Group>
          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">رشته تحصیلی</Form.Label>
            <Form.Control
              id="reported-faculty-field"
              type="text"
              className="form-control"
              required
              placeholder="رشته تحصیلی خود را وارد کنید"
              value={step3.reportedField || ''}
              onChange={(e) => setStep3({ reportedField: e.target.value })}
            />

            <div className="invalid-feedback" style={{ display: validationErrors.reportedField ? 'block' : 'none' }}>
              {validationErrors.reportedField}
            </div>
          </Form.Group>
          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">سنوات خدمت</Form.Label>
            <Form.Control
              id="reported-faculty-years-of-service"
              type="text"
              className="form-control"
              required
              placeholder="سنوات خدمت خود را وارد کنید"
              value={step3.reportedYearsOfService || ''}
              onChange={(e) => setStep3({ reportedYearsOfService: e.target.value })}
            />
            <div
              className="invalid-feedback"
              style={{ display: validationErrors.reportedYearsOfService ? 'block' : 'none' }}
            >
              {validationErrors.reportedYearsOfService}
            </div>
          </Form.Group>
          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">مقطع تحصیلی</Form.Label>
            <Select
              options={FORM_OPTIONS.educationLevel}
              placeholder="مقطع تحصیلی خود را انتخاب کنید"
              classNamePrefix="Select2"
              isSearchable
              value={FORM_OPTIONS.educationLevel.find((option) => option.value === step3.reportedEducationLevel)}
              onChange={(e) => setStep3({ reportedEducationLevel: e.value })}
            />
            <div
              className="invalid-feedback"
              style={{ display: validationErrors.reportedEducationLevel ? 'block' : 'none' }}
            >
              {validationErrors.reportedEducationLevel}
            </div>
          </Form.Group>
        </Fragment>
      )}

      {step3.reportedRole === 'student' && (
        <Fragment>
          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">رشته تحصیلی</Form.Label>
            <Form.Control
              id="reported-student-field"
              type="text"
              className="form-control"
              required
              placeholder="رشته تحصیلی خود را وارد کنید"
              value={step3.reportedField || ''}
              onChange={(e) => setStep3({ reportedField: e.target.value })}
            />
            <div className="invalid-feedback" style={{ display: validationErrors.reportedField ? 'block' : 'none' }}>
              {validationErrors.reportedField}
            </div>
          </Form.Group>
          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">مقطع تحصیلی</Form.Label>
            <Select
              options={FORM_OPTIONS.educationLevel}
              placeholder="مقطع تحصیلی خود را انتخاب کنید"
              classNamePrefix="Select2"
              isSearchable
              value={FORM_OPTIONS.educationLevel.find((option) => option.value === step3.reportedEducationLevel)}
              onChange={(e) => setStep3({ reportedEducationLevel: e.value })}
            />
            <div
              className="invalid-feedback"
              style={{ display: validationErrors.reportedEducationLevel ? 'block' : 'none' }}
            >
              {validationErrors.reportedEducationLevel}
            </div>
          </Form.Group>
          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">وضعیت تحصیلی</Form.Label>
            <Select
              options={FORM_OPTIONS.academicStatus}
              value={FORM_OPTIONS.academicStatus.find((option) => option.value === step3.reportedAcademicStatus)}
              placeholder="وضعیت تحصیلی خود را انتخاب کنید"
              classNamePrefix="Select2"
              isSearchable
              onChange={(e) => setStep3({ reportedAcademicStatus: e.value })}
            />
            <div
              className="invalid-feedback"
              style={{ display: validationErrors.reportedAcademicStatus ? 'block' : 'none' }}
            >
              {validationErrors.reportedAcademicStatus}
            </div>
          </Form.Group>
        </Fragment>
      )}

      {step3.reportedRole === 'non_faculty_member' && (
        <Fragment>
          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">سمت اجرایی</Form.Label>
            <Form.Control
              id="reported-non-faculty-admin-position"
              type="text"
              className="form-control"
              required
              placeholder="سمت اجرایی خود را وارد کنید"
              value={step3.reportedAdministrativePosition || ''}
              onChange={(e) => setStep3({ reportedAdministrativePosition: e.target.value })}
            />
            <div
              className="invalid-feedback"
              style={{ display: validationErrors.reportedAdministrativePosition ? 'block' : 'none' }}
            >
              {validationErrors.reportedAdministrativePosition}
            </div>
          </Form.Group>
          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">مقطع تحصیلی</Form.Label>
            <Select
              options={FORM_OPTIONS.educationLevel}
              placeholder="مقطع تحصیلی خود را انتخاب کنید"
              classNamePrefix="Select2"
              value={FORM_OPTIONS.educationLevel.find((option) => option.value === step3.reportedEducationLevel)}
              isSearchable
              onChange={(e) => setStep3({ reportedEducationLevel: e.value })}
            />
            <div
              className="invalid-feedback"
              style={{ display: validationErrors.reportedEducationLevel ? 'block' : 'none' }}
            >
              {validationErrors.reportedEducationLevel}
            </div>
          </Form.Group>
        </Fragment>
      )}

      {step3.reportedRole === 'independent_researcher' && (
        <Fragment>
          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">مقطع تحصیلی</Form.Label>
            <Select
              options={FORM_OPTIONS.educationLevel}
              placeholder="مقطع تحصیلی خود را انتخاب کنید"
              classNamePrefix="Select2"
              isSearchable
              value={FORM_OPTIONS.educationLevel.find((option) => option.value === step3.reportedEducationLevel)}
              onChange={(e) => setStep3({ reportedEducationLevel: e.value })}
            />
            <div
              className="invalid-feedback"
              style={{ display: validationErrors.reportedEducationLevel ? 'block' : 'none' }}
            >
              {validationErrors.reportedEducationLevel}
            </div>
          </Form.Group>
        </Fragment>
      )}

      {/* Other roles (institute_president, institute_vice_president, board_member, trustee_member, ethics_committee_member) */}
      {[
        'institute_president',
        'institute_vice_president',
        'board_member',
        'trustee_member',
        'ethics_committee_member',
      ].includes(step3.reportedRole) && (
        <Fragment>
          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">مقطع تحصیلی</Form.Label>
            <Select
              options={FORM_OPTIONS.educationLevel}
              placeholder="مقطع تحصیلی خود را انتخاب کنید"
              classNamePrefix="Select2"
              isSearchable
              value={FORM_OPTIONS.educationLevel.find((option) => option.value === step3.reportedEducationLevel)}
              onChange={(e) => setStep3({ reportedEducationLevel: e.value })}
            />
            <div
              className="invalid-feedback"
              style={{ display: validationErrors.reportedEducationLevel ? 'block' : 'none' }}
            >
              {validationErrors.reportedEducationLevel}
            </div>
          </Form.Group>
        </Fragment>
      )}

      {/* سابقه تخلف پژوهشی */}
      <h5 className={'mt-5'}>سابقه تخلف پژوهشی</h5>
      <Form.Group className="form-group">
        <Form.Label className="form-label">سابقه تخلف پژوهشی دارد</Form.Label>
        <div className="d-flex flex-row align-items-center gap-4">
          <Form.Label className="custom-control custom-radio">
            <Form.Control
              type="radio"
              className="custom-control-input"
              name="reported-has-violation"
              value="no"
              checked={step3.reportedHasViolation === 'no'}
              onChange={() => setStep3({ reportedHasViolation: 'no' })}
            />
            <span className="custom-control-label">خیر</span>
          </Form.Label>
          <Form.Label className="custom-control custom-radio">
            <Form.Control
              type="radio"
              className="custom-control-input"
              name="reported-has-violation"
              value="yes"
              checked={step3.reportedHasViolation === 'yes'}
              onChange={() => setStep3({ reportedHasViolation: 'yes' })}
            />
            <span className="custom-control-label">بلی</span>
          </Form.Label>
        </div>
        <div className="invalid-feedback" style={{ display: validationErrors.reportedHasViolation ? 'block' : 'none' }}>
          {validationErrors.reportedHasViolation}
        </div>
      </Form.Group>

      {step3.reportedHasViolation === 'yes' && (
        <Fragment>
          <Form.Group className="form-group">
            <Form.Label className="form-label">تخلف فعلی تکرار تخلفات قبلی است</Form.Label>
            <div className="d-flex flex-row align-items-center gap-4">
              <Form.Label className="custom-control custom-radio">
                <Form.Control
                  type="radio"
                  className="custom-control-input"
                  name="reported-repeat-violation"
                  value="yes"
                  checked={step3.reportedRepeatViolation === 'yes'}
                  onChange={() => setStep3({ reportedRepeatViolation: 'yes' })}
                />
                <span className="custom-control-label">بلی</span>
              </Form.Label>
              <Form.Label className="custom-control custom-radio">
                <Form.Control
                  type="radio"
                  className="custom-control-input"
                  name="reported-repeat-violation"
                  value="no"
                  checked={step3.reportedRepeatViolation === 'no'}
                  onChange={() => setStep3({ reportedRepeatViolation: 'no' })}
                />
                <span className="custom-control-label">خیر</span>
              </Form.Label>
            </div>
            <div
              className="invalid-feedback"
              style={{ display: validationErrors.reportedRepeatViolation ? 'block' : 'none' }}
            >
              {validationErrors.reportedRepeatViolation}
            </div>
          </Form.Group>
          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">انواع تخلفاتی که تاکنون انجام داده است</Form.Label>
            <Form.Control
              id="reported-violation-types"
              type="text"
              className="form-control"
              required
              placeholder="انواع تخلفات را وارد کنید"
              value={step3.reportedViolationTypes || ''}
              onChange={(e) => setStep3({ reportedViolationTypes: e.target.value })}
            />
            <div
              className="invalid-feedback"
              style={{ display: validationErrors.reportedViolationTypes ? 'block' : 'none' }}
            >
              {validationErrors.reportedViolationTypes}
            </div>
          </Form.Group>
          <Form.Group className="form-group">
            <Form.Label className="form-label">تخلفات قبلی در چند موسسه مختلف بوده‌اند</Form.Label>
            <div className="d-flex flex-row align-items-center gap-4">
              <Form.Label className="custom-control custom-radio">
                <Form.Control
                  type="radio"
                  className="custom-control-input"
                  name="reported-multi-institute-violations"
                  value="yes"
                  checked={step3.reportedMultiInstituteViolations === 'yes'}
                  onChange={() => setStep3({ reportedMultiInstituteViolations: 'yes' })}
                />
                <span className="custom-control-label">بلی</span>
              </Form.Label>
              <Form.Label className="custom-control custom-radio">
                <Form.Control
                  type="radio"
                  className="custom-control-input"
                  name="reported-multi-institute-violations"
                  value="no"
                  checked={step3.reportedMultiInstituteViolations === 'no'}
                  onChange={() => setStep3({ reportedMultiInstituteViolations: 'no' })}
                />
                <span className="custom-control-label">خیر</span>
              </Form.Label>
            </div>
            <div
              className="invalid-feedback"
              style={{ display: validationErrors.reportedMultiInstituteViolations ? 'block' : 'none' }}
            >
              {validationErrors.reportedMultiInstituteViolations}
            </div>
          </Form.Group>
          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">سابقه تخلفات قبلی (شماره پرونده‌ها)</Form.Label>
            <Form.Control
              id="reported-violation-cases"
              type="text"
              className="form-control"
              placeholder="شماره پرونده‌های تخلفات قبلی را وارد کنید"
              value={step3.reportedViolationCases || ''}
              onChange={(e) => setStep3({ reportedViolationCases: e.target.value })}
            />
            <div
              className="invalid-feedback"
              style={{ display: validationErrors.reportedViolationCases ? 'block' : 'none' }}
            >
              {validationErrors.reportedViolationCases}
            </div>
          </Form.Group>
        </Fragment>
      )}

      {/* موسسه محل وقوع تخلف */}
      {step3.reportedViolationInstitutes.map((institute, index) => (
        <Fragment key={index}>
          <h5 className={'mt-5'}>موسسه محل وقوع تخلف {index !== 0 ? index + 1 : ''}</h5>
          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">نام دانشگاه</Form.Label>
            <Select
              options={institutes}
              placeholder={loading ? 'در حال بارگذاری...' : 'دانشگاه را انتخاب کنید'}
              classNamePrefix="Select2"
              isLoading={loading}
              isDisabled={loading}
              value={
                institutes.find((option) => option.value === step3.reportedViolationInstitutes[index].university) ||
                null
              }
              isSearchable
              onChange={(e) =>
                setStep3({
                  reportedViolationInstitutes: step3.reportedViolationInstitutes.map((inst, i) =>
                    i === index
                      ? {
                          ...inst,
                          university: e?.value || '',
                          customUniversity: e?.value === 'other' ? '' : inst.customUniversity,
                        }
                      : inst
                  ),
                })
              }
            />
            <div
              className="invalid-feedback"
              style={{ display: validationErrors[`institute_university_${index}`] ? 'block' : 'none' }}
            >
              {validationErrors[`institute_university_${index}`]}
            </div>
          </Form.Group>
          {institute.university === 'other' && (
            <Form.Group className="control-group form-group">
              <Form.Label className="form-label">نام موسسه</Form.Label>
              <Form.Control
                id={`reported-violation-custom-university-${index}`}
                type="text"
                className="form-control"
                required
                placeholder="نام موسسه"
                value={institute.customUniversity || ''}
                onChange={(e) =>
                  setStep3({
                    reportedViolationInstitutes: step3.reportedViolationInstitutes.map((inst, i) =>
                      i === index ? { ...inst, customUniversity: e.target.value } : inst
                    ),
                  })
                }
              />
              <div
                className="invalid-feedback"
                style={{ display: validationErrors[`institute_customUniversity_${index}`] ? 'block' : 'none' }}
              >
                {validationErrors[`institute_customUniversity_${index}`]}
              </div>
            </Form.Group>
          )}
          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">سطح تیپ دانشگاه</Form.Label>
            <Select
              options={FORM_OPTIONS.instituteCategory}
              value={FORM_OPTIONS.instituteCategory.find(
                (option) => option.value === step3.reportedViolationInstitutes[index].universityType
              )}
              placeholder="سطح تیپ دانشگاه را انتخاب کنید"
              classNamePrefix="Select2"
              isSearchable
              onChange={(e) =>
                setStep3({
                  reportedViolationInstitutes: step3.reportedViolationInstitutes.map((inst, i) =>
                    i === index ? { ...inst, universityType: e.value } : inst
                  ),
                })
              }
            />
            <div
              className="invalid-feedback"
              style={{ display: validationErrors[`institute_universityType_${index}`] ? 'block' : 'none' }}
            >
              {validationErrors[`institute_universityType_${index}`]}
            </div>
          </Form.Group>
          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">نوع موسسه</Form.Label>
            <Select
              options={FORM_OPTIONS.instituteType}
              value={FORM_OPTIONS.instituteType.find(
                (option) => option.value === step3.reportedViolationInstitutes[index]?.instituteType
              )}
              placeholder="نوع موسسه را انتخاب کنید"
              classNamePrefix="Select2"
              isSearchable
              onChange={(e) =>
                setStep3({
                  reportedViolationInstitutes: step3.reportedViolationInstitutes.map((inst, i) =>
                    i === index ? { ...inst, instituteType: e.value } : inst
                  ),
                })
              }
            />
            <div
              className="invalid-feedback"
              style={{ display: validationErrors[`institute_instituteType_${index}`] ? 'block' : 'none' }}
            >
              {validationErrors[`institute_instituteType_${index}`]}
            </div>
          </Form.Group>
          <Form.Group className="form-group">
            <Form.Label className="form-label">محل جغرافیایی موسسه</Form.Label>
            <div className="d-flex flex-row align-items-center gap-4">
              <Form.Label className="custom-control custom-radio">
                <Form.Control
                  type="radio"
                  className="custom-control-input"
                  name={`institute-location-${index}`}
                  value="domestic"
                  checked={institute.location === 'domestic'}
                  onChange={() =>
                    setStep3({
                      reportedViolationInstitutes: step3.reportedViolationInstitutes.map((inst, i) =>
                        i === index ? { ...inst, location: 'domestic' } : inst
                      ),
                    })
                  }
                />
                <span className="custom-control-label">داخل کشور</span>
              </Form.Label>
              <Form.Label className="custom-control custom-radio">
                <Form.Control
                  type="radio"
                  className="custom-control-input"
                  name={`institute-location-${index}`}
                  value="international"
                  checked={institute.location === 'international'}
                  onChange={() =>
                    setStep3({
                      reportedViolationInstitutes: step3.reportedViolationInstitutes.map((inst, i) =>
                        i === index ? { ...inst, location: 'international' } : inst
                      ),
                    })
                  }
                />
                <span className="custom-control-label">خارج کشور</span>
              </Form.Label>
            </div>
            <div
              className="invalid-feedback"
              style={{ display: validationErrors[`institute_location_${index}`] ? 'block' : 'none' }}
            >
              {validationErrors[`institute_location_${index}`]}
            </div>
          </Form.Group>
          <Form.Group className="form-group">
            <Form.Label className="form-label">موسسه دارای کارگروه اخلاق در پژوهش است</Form.Label>
            <div className="d-flex flex-row align-items-center gap-4">
              <Form.Label className="custom-control custom-radio">
                <Form.Control
                  type="radio"
                  className="custom-control-input"
                  name={`institute-ethics-committee-${index}`}
                  value="yes"
                  checked={institute.hasEthicsCommittee === 'yes'}
                  onChange={() =>
                    setStep3({
                      reportedViolationInstitutes: step3.reportedViolationInstitutes.map((inst, i) =>
                        i === index ? { ...inst, hasEthicsCommittee: 'yes' } : inst
                      ),
                    })
                  }
                />
                <span className="custom-control-label">بلی</span>
              </Form.Label>
              <Form.Label className="custom-control custom-radio">
                <Form.Control
                  type="radio"
                  className="custom-control-input"
                  name={`institute-ethics-committee-${index}`}
                  value="no"
                  checked={institute.hasEthicsCommittee === 'no'}
                  onChange={() =>
                    setStep3({
                      reportedViolationInstitutes: step3.reportedViolationInstitutes.map((inst, i) =>
                        i === index ? { ...inst, hasEthicsCommittee: 'no' } : inst
                      ),
                    })
                  }
                />
                <span className="custom-control-label">خیر</span>
              </Form.Label>
            </div>
            <div
              className="invalid-feedback"
              style={{ display: validationErrors[`institute_hasEthicsCommittee_${index}`] ? 'block' : 'none' }}
            >
              {validationErrors[`institute_hasEthicsCommittee_${index}`]}
            </div>
          </Form.Group>
          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">رابطه سازمانی گزارش شونده با موسسه محل وقوع تخلف</Form.Label>
            <Select
              options={FORM_OPTIONS.organizationalRole}
              value={FORM_OPTIONS.organizationalRole.find(
                (option) => option.value === step3.reportedViolationInstitutes[index].reportedRelation
              )}
              placeholder="رابطه سازمانی را انتخاب کنید"
              classNamePrefix="Select2"
              isSearchable
              onChange={(e) =>
                setStep3({
                  reportedViolationInstitutes: step3.reportedViolationInstitutes.map((inst, i) =>
                    i === index ? { ...inst, reportedRelation: e.value } : inst
                  ),
                })
              }
            />
            <div
              className="invalid-feedback"
              style={{ display: validationErrors[`institute_reportedRelation_${index}`] ? 'block' : 'none' }}
            >
              {validationErrors[`institute_reportedRelation_${index}`]}
            </div>
          </Form.Group>
          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">رابطه سازمانی گزارش‌دهنده با موسسه محل وقوع تخلف</Form.Label>
            <Select
              options={FORM_OPTIONS.organizationalRole}
              value={FORM_OPTIONS.organizationalRole.find((option) => option.value === step3.reportedRelation)}
              placeholder="رابطه سازمانی را انتخاب کنید"
              classNamePrefix="Select2"
              isSearchable
              onChange={(e) =>
                setStep3({
                  reportedViolationInstitutes: step3.reportedViolationInstitutes.map((inst, i) =>
                    i === index ? { ...inst, reporterRelation: e.value } : inst
                  ),
                })
              }
            />
            <div
              className="invalid-feedback"
              style={{ display: validationErrors[`institute_reporterRelation_${index}`] ? 'block' : 'none' }}
            >
              {validationErrors[`institute_reporterRelation_${index}`]}
            </div>
          </Form.Group>
          {index > 0 && (
            <Button
              variant="danger"
              onClick={() =>
                setStep3({
                  reportedViolationInstitutes: step3.reportedViolationInstitutes.filter((_, i) => i !== index),
                })
              }
            >
              حذف موسسه
            </Button>
          )}
        </Fragment>
      ))}

      <Form.Group className="form-group">
        <Form.Label className="form-label">تخلف فعلی در چند موسسه مختلف واقع شده است</Form.Label>
        <div className="d-flex flex-row align-items-center gap-4">
          <Form.Label className="custom-control custom-radio">
            <Form.Control
              type="radio"
              className="custom-control-input"
              name="reported-violation-multi-institutes"
              value="yes"
              checked={step3.reportedViolationMultipleInstitutes === 'yes'}
              onChange={() =>
                setStep3({
                  reportedViolationMultipleInstitutes: 'yes',
                  reportedViolationInstitutes: [
                    ...step3.reportedViolationInstitutes,
                    {
                      university: '',
                      universityType: '',
                      instituteType: '',
                      location: '',
                      hasEthicsCommittee: '',
                      reportedRelation: '',
                      reporterRelation: '',
                    },
                  ],
                })
              }
            />
            <span className="custom-control-label">بلی</span>
          </Form.Label>
          <Form.Label className="custom-control custom-radio">
            <Form.Control
              type="radio"
              className="custom-control-input"
              name="reported-violation-multi-institutes"
              value="no"
              checked={step3.reportedViolationMultipleInstitutes === 'no'}
              onChange={() =>
                setStep3({
                  reportedViolationMultipleInstitutes: 'no',
                  reportedViolationInstitutes: [
                    {
                      university: '',
                      universityType: '',
                      instituteType: '',
                      location: '',
                      hasEthicsCommittee: '',
                      reportedRelation: '',
                      reporterRelation: '',
                    },
                  ],
                })
              }
            />
            <span className="custom-control-label">خیر</span>
          </Form.Label>
        </div>
      </Form.Group>
    </section>
  );
};

export default Step3;
