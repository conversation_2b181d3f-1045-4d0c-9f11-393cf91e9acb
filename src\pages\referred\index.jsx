import { Fragment, useEffect, useState } from 'react';
import { Card, Col, Row, Button, Form, Alert } from 'react-bootstrap';
import { useParams, useNavigate } from 'react-router-dom';
import Pageheader from 'layout/layoutcomponent/pageheader.jsx';
import useGeneralStore from 'store/generalStore.js';
import DossierService from 'service/api/dossierService.js';
import toastService from 'utils/toastService.js';
import Loader from 'layout/layoutcomponent/loaders';
import DatePicker from 'react-multi-date-picker';
import persian from 'react-date-object/calendars/persian';
import persian_fa from 'react-date-object/locales/persian_fa';
import { formatDate } from '../../utils/helper';
import { getFormOptions } from '../../utils/formOptions';
import Select from 'react-select';

const Referred = () => {
  const { uuid } = useParams();
  const navigate = useNavigate();
  const { setData } = useGeneralStore();

  // Get authority options from formOptions
  const authorities = getFormOptions('authorities');

  const [dossier, setDossier] = useState(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [formData, setFormData] = useState({
    referralDate: '',
    authority: {},
    documents: [],
  });

  // Fetch dossier data
  useEffect(() => {
    const fetchDossierData = async () => {
      if (!uuid) {
        toastService.error('شناسه پرونده معتبر نیست');
        navigate('/app/inquiry');
        return;
      }

      try {
        setLoading(true);
        const response = await DossierService.getDossier(uuid);
        const dossierData = response?.data?.data;

        if (dossierData) {
          setDossier(dossierData);
          setData({ pageTitle: `ارجاع به مراجع ذیصلاح - پرونده ${dossierData.dossier_code || ''}` });

          // Check if dossier is in correct status
          if (dossierData.dossier_status !== 'referred') {
            toastService.error('این پرونده در مرحله ارجاع به مراجع ذیصلاح نیست');
            navigate('/app/inquiry');
            return;
          }

          // Load existing data if available
          if (dossierData.referred) {
            setFormData({
              referralDate: dossierData.referred.referralDate || '',
              authority: dossierData.referred.authority || '',
              documents: dossierData.referred.documents || [],
            });
          }
        } else {
          throw new Error('Dossier data not found');
        }
      } catch (error) {
        console.error('Error fetching dossier:', error);
        toastService.error('خطا در دریافت اطلاعات پرونده');
        navigate('/app/inquiry');
      } finally {
        setLoading(false);
      }
    };

    fetchDossierData();
  }, [uuid, setData, navigate]);

  // Handle form input changes
  const handleInputChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle file upload
  const handleFileUpload = async (event) => {
    const files = Array.from(event.target.files);
    if (files.length === 0) return;

    try {
      setUploading(true);
      for (const file of files) {
        try {
          const formData = new FormData();
          formData.append('document', file);
          formData.append('document_source', 'referred');
          formData.append('dossier', uuid);

          await DossierService.uploadDocument(formData);
          toastService.success(`فایل ${file.name} با موفقیت آپلود شد`);

          // Add to local state for display
          setFormData((prev) => ({
            ...prev,
            documents: [...prev.documents, file],
          }));
        } catch (error) {
          console.error('Error uploading file:', error);
          toastService.error(`خطا در آپلود فایل ${file.name}`);
        }
      }
    } catch (error) {
      console.error('Error uploading files:', error);
      toastService.error('خطا در آپلود فایل‌ها');
    } finally {
      setUploading(false);
    }
  };

  // Remove uploaded document
  const removeDocument = (index) => {
    const newDocs = formData.documents.filter((_, i) => i !== index);
    handleInputChange('documents', newDocs);
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formData.referralDate || !formData.authority) {
      toastService.error('لطفاً تمام فیلدهای الزامی را تکمیل کنید');
      return;
    }

    if (formData.documents.length === 0) {
      toastService.error('بارگذاری حداقل یک فایل الزامی است');
      return;
    }

    try {
      setSubmitting(true);

      // Submit referral data
      const referralData = {
        referred: {
          session_date: formData.referralDate,
          reviewing_authority: formData.authority?.value || '---',
        },
      };

      const response = await DossierService.updateReferredDossier(uuid, referralData);

      if (response?.data?.status === 'OK') {
        await DossierService.submitDossier(uuid, { description: '---' });
        toastService.success('ارجاع به مراجع ذیصلاح با موفقیت ثبت شد');
        navigate('/app/inquiry');
      }
    } catch (error) {
      console.error('Error submitting referral:', error);
      toastService.error('خطا در ثبت ارجاع');
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return <Loader />;
  }

  if (!dossier) {
    return (
      <Fragment>
        <Pageheader title="خطا" />
        <Row>
          <Col lg={12}>
            <Card>
              <Card.Body className="text-center">
                <h4>پرونده یافت نشد</h4>
                <Button variant="primary" onClick={() => navigate('/app/inquiry')}>
                  بازگشت به لیست پرونده‌ها
                </Button>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Fragment>
    );
  }

  return (
    <Fragment>
      <Pageheader title={`ارجاع به مراجع ذیصلاح - پرونده ${dossier.dossier_code || ''}`} />

      {/* Action Buttons */}
      <Row className="mb-3">
        <Col lg={12}>
          <div className="d-flex justify-content-between align-items-center">
            <Button variant="outline-secondary" onClick={() => navigate('/app/inquiry')}>
              <i className="fe fe-arrow-right me-2"></i>
              بازگشت به لیست پرونده‌ها
            </Button>

            <Button variant="outline-info" onClick={() => navigate(`/app/dossier/${uuid}/view`)}>
              <i className="fe fe-eye me-2"></i>
              مشاهده کامل پرونده
            </Button>
          </div>
        </Col>
      </Row>

      <Row>
        {/* Dossier Summary */}
        <Col lg={4}>
          <Card className="mb-4">
            <Card.Header>
              <Card.Title className="mb-0">
                <i className="fe fe-info me-2"></i>
                خلاصه پرونده
              </Card.Title>
            </Card.Header>
            <Card.Body>
              <div className="mb-3">
                <strong>شماره پرونده:</strong> {dossier.dossier_code || '---'}
              </div>
              <div className="mb-3">
                <strong>موسسه:</strong> {dossier.institute.title || 'نامشخص'}
              </div>
              <div className="mb-3">
                <strong>گزارش‌دهنده:</strong> {dossier.reporter?.full_name || 'نامشخص'}
              </div>
              <div className="mb-3">
                <strong>گزارش‌شونده:</strong> {dossier.reportee?.full_name || 'نامشخص'}
              </div>

              {/* Show final opinion summary if available */}
              {dossier.final_opinion && (
                <Alert variant="success" className="mt-3">
                  <i className="fe fe-check-circle me-2"></i>
                  <strong>نظریه نهایی کارگروه:</strong>
                  <br />
                  <small>تاریخ جلسه: {dossier.final_opinion.meetingDate}</small>
                </Alert>
              )}
            </Card.Body>
          </Card>
        </Col>

        {/* Referral Form */}
        <Col lg={8}>
          <Card>
            <Card.Header>
              <Card.Title className="mb-0">
                <i className="fe fe-send me-2"></i>
                ارجاع به مراجع ذیصلاح
              </Card.Title>
            </Card.Header>
            <Card.Body>
              <Alert variant="danger">
                <i className="fe fe-alert-triangle me-2"></i>
                <strong>ارجاع به مراجع ذیصلاح</strong>
                <br />
                با توجه به نظریه نهایی کارگروه، پرونده باید به مراجع ذی‌صلاح ارجاع شود.
              </Alert>

              <Form onSubmit={handleSubmit}>
                {/* Referral Date */}
                <Row className="mb-3">
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label>
                        <strong>تاریخ ارجاع:</strong>
                        <span className="text-danger ms-1">*</span>
                      </Form.Label>
                      <DatePicker
                        value={formData.referralDate ? new Date(formData.referralDate) : null}
                        calendar={persian}
                        locale={persian_fa}
                        calendarPosition="bottom-right"
                        containerClassName={'form-control'}
                        style={{
                          background: 'transparent',
                          width: '100%',
                          boxShadow: 'none!important',
                          outline: 'none',
                          border: 'none',
                        }}
                        onChange={(date) => {
                          const formattedDate = date ? formatDate(new Date(date)) : '';
                          handleInputChange('referralDate', formattedDate);
                        }}
                        placeholder="تاریخ ارجاع را انتخاب کنید"
                      />
                      <Form.Text className="text-muted">تاریخ ارجاع پرونده به مرجع ذی‌صلاح</Form.Text>
                    </Form.Group>
                  </Col>
                </Row>

                {/* Authority Selection */}
                <Row className="mb-3">
                  <Col md={12}>
                    <Form.Group className="control-group form-group">
                      <Form.Label className="form-label">
                        <strong>مرجع ذیصلاح:</strong>
                        <span className="text-danger ms-1">*</span>
                      </Form.Label>
                      <Select
                        options={authorities.map((authority) => ({ value: authority.value, label: authority.label }))}
                        placeholder={'مرجع ذیصلاح را انتخاب کنید'}
                        classNamePrefix="Select2"
                        isSearchable
                        // value={{ value: formData.authority.value, label: formData.authority.label }}
                        onChange={(e) => handleInputChange('authority', e)}
                        required
                      ></Select>
                      <Form.Text className="text-muted">مرجع ذی‌صلاح که پرونده به آن ارجاع می‌شود</Form.Text>
                    </Form.Group>
                  </Col>
                </Row>

                {/* Document Upload */}
                <Row className="mb-3">
                  <Col md={12}>
                    <Form.Group>
                      <Form.Label>
                        <strong>بارگذاری مستندات :</strong>
                        <span className="text-danger ms-1">*</span>
                      </Form.Label>
                      <Form.Control
                        type="file"
                        multiple
                        accept=".pdf,.doc,.docx"
                        onChange={handleFileUpload}
                        disabled={uploading}
                      />
                      <Form.Text className="text-muted">
                        فرمت‌های مجاز: PDF, DOC, DOCX - بارگذاری مستندات ارجاع الزامی است
                      </Form.Text>
                    </Form.Group>
                  </Col>
                </Row>

                {/* Uploaded Documents List */}
                {formData.documents.length > 0 && (
                  <Row className="mb-3">
                    <Col md={12}>
                      <Form.Label>
                        <strong>فایل‌های آپلود شده:</strong>
                      </Form.Label>
                      <div className="border rounded p-3">
                        {formData.documents.map((doc, index) => (
                          <div key={index} className="d-flex justify-content-between align-items-center mb-2">
                            <div className="d-flex align-items-center">
                              <i className="fe fe-file me-2"></i>
                              <span>{doc.name || `فایل ${index + 1}`}</span>
                            </div>
                            <Button variant="outline-danger" size="sm" onClick={() => removeDocument(index)}>
                              <i className="fe fe-trash-2"></i>
                            </Button>
                          </div>
                        ))}
                      </div>
                    </Col>
                  </Row>
                )}

                {/* Submit Button */}
                <div className="d-flex justify-content-end mt-4">
                  <Button type="submit" variant="success" disabled={submitting}>
                    {submitting ? (
                      <>
                        <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        در حال ثبت...
                      </>
                    ) : (
                      <>
                        <i className="fe fe-check me-2"></i>
                        ثبت ارجاع و انتقال به مرحله بعد
                      </>
                    )}
                  </Button>
                </div>

                {/* Process Information */}
                <Alert variant="warning" className="mt-3">
                  <i className="fe fe-info me-2"></i>
                  <strong>مرحله بعدی:</strong> پس از ارجاع پرونده به مرجع ذی‌صلاح، باید منتظر صدور حکم یا رای از سوی آن
                  مرجع باشید. این فرآیند ممکن است زمان‌بر باشد.
                </Alert>
              </Form>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Fragment>
  );
};

export default Referred;
