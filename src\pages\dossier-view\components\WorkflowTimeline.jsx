import { Card, Badge } from 'react-bootstrap';
import { formatJalaliDate } from 'utils/helper.js';

// Status configuration (same as in inquiry page)
const STATUS_CONFIG = {
  draft: {
    label: 'پیش نویس',
    color: 'primary',
    icon: 'fe fe-edit',
    description: 'پرونده در حال تکمیل اطلاعات',
  },
  expert_review: {
    label: 'داوری و کارشناسی',
    color: 'primary',
    icon: 'fe fe-search',
    description: 'پرونده در مرحله بررسی و کارشناسی',
  },
  initial_opinion: {
    label: 'صدور نظریه بدوی کارگروه موسسه',
    color: 'primary',
    icon: 'fe fe-file-text',
    description: 'نظریه اولیه کارگروه صادر شده',
  },
  appeal_request: {
    label: 'درخواست تجدید نظر و ارسال به کارگروه وزارتی',
    color: 'primary',
    icon: 'fe fe-refresh-cw',
    description: 'درخواست تجدید نظر ارسال شده',
  },
  ministry_opinion: {
    label: 'صدور نظریه کارگروه وزارتی',
    color: 'primary',
    icon: 'fe fe-award',
    description: 'نظریه کارگروه وزارتی صادر شده',
  },
  final_opinion: {
    label: 'صدور نظریه نهایی کارگروه موسسه',
    color: 'primary',
    icon: 'fe fe-check-circle',
    description: 'نظریه نهایی کارگروه صادر شده',
  },
  referred: {
    label: 'ارجاع به مراجع ذیصلاح',
    color: 'primary',
    icon: 'fe fe-send',
    description: 'پرونده به مراجع ذیصلاح ارجاع شده',
  },
  ruling_issued: {
    label: 'صدور حکم/رای مراجع ذیصلاح',
    color: 'primary',
    icon: 'fe fe-bookmark',
    description: 'حکم یا رای نهایی صادر شده',
  },
  completed: {
    label: 'خاتمه یافته',
    color: 'dark',
    icon: 'fe fe-check',
    description: 'پرونده به پایان رسیده',
  },
};

// Get workflow steps in order
const getWorkflowSteps = () => {
  return [
    'draft',
    'expert_review',
    'initial_opinion',
    'appeal_request',
    'ministry_opinion',
    'final_opinion',
    'referred',
    'ruling_issued',
    'completed',
  ];
};

const WorkflowTimeline = ({ dossier }) => {
  const currentStatus = dossier?.dossier_status;
  const workflowSteps = getWorkflowSteps();
  const currentStepIndex = workflowSteps.indexOf(currentStatus);

  // Get step status (completed, current, pending)
  const getStepStatus = (stepIndex) => {
    if (stepIndex < currentStepIndex) return 'completed';
    if (stepIndex === currentStepIndex) return 'current';
    return 'pending';
  };

  // Get step color based on status
  const getStepColor = (step, status) => {
    if (status === 'completed') return 'primary';
    if (status === 'current') return 'secondary';
    return 'light';
  };

  return (
    <Card>
      <Card.Header>
        <Card.Title className="mb-0">
          <i className="fe fe-activity me-2"></i>
          گردش کار پرونده
        </Card.Title>
      </Card.Header>
      <Card.Body>
        <div className="activity-list">
          {workflowSteps.map((step, index) => {
            const config = STATUS_CONFIG[step];
            const status = getStepStatus(index);
            const color = getStepColor(step, status);

            return (
              <div key={step} className="activity">
                <div className={`img-activity bg-${color}`}>
                  <i className={`${config.icon} text-white`}></i>
                </div>
                <div className="item-activity">
                  <div className="d-flex justify-content-between align-items-start mb-1">
                    <h6 className="mb-1">{config.label}</h6>
                    <Badge
                      bg={status === 'completed' ? 'primary' : status === 'current' ? color : 'light'}
                      text={status === 'pending' ? 'dark' : 'white'}
                    >
                      {status === 'completed' ? 'تکمیل شده' : status === 'current' ? 'جاری' : 'در انتظار'}
                    </Badge>
                  </div>
                  <p className="text-muted mb-1 small">{config.description}</p>
                  {status === 'current' && (
                    <small className="text-secondary">
                      <i className="fe fe-clock me-1"></i>
                      آخرین به‌روزرسانی: {formatJalaliDate(dossier.updated_at)}
                    </small>
                  )}
                  {status === 'completed' && index < currentStepIndex && (
                    <small className="text-primary">
                      <i className="fe fe-check me-1"></i>
                      تکمیل شده
                    </small>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        {/* Current Status Summary */}
        <div className="mt-4 p-3 bg-light rounded">
          <h6 className="mb-2">وضعیت فعلی:</h6>
          <div className="d-flex align-items-center">
            <Badge bg={STATUS_CONFIG[currentStatus]?.color || 'secondary'} className="me-2">
              {STATUS_CONFIG[currentStatus]?.label || 'نامشخص'}
            </Badge>
            <small className="text-muted">از تاریخ {formatJalaliDate(dossier.updated_at)}</small>
          </div>
        </div>
      </Card.Body>
    </Card>
  );
};

export default WorkflowTimeline;
