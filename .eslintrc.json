{"env": {"browser": true, "es2021": true, "node": true}, "extends": ["plugin:react/recommended", "standard"], "overrides": [], "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["react"], "rules": {"no-multiple-empty-lines": ["error", {"max": 1, "maxEOF": 1}], "no-console": "off", "eol-last": "error", "block-spacing": "error", "vars-on-top": "error", "no-useless-return": "error", "no-useless-escape": "error", "no-lone-blocks": "error", "no-empty": "error", "camelcase": "error", "linebreak-style": "error", "semi": "error", "no-empty-function": "off", "no-whitespace-before-property": "error", "react/no-unescaped-entities": "off", "no-unused-vars": "off", "no-duplicate-imports": "error", "react/jsx-key": "off", "react/prop-types": "off", "eslint no-new": "off", "no-empty-interface": "off", "eslint no-tabs": [{"allowIndentationTabs": false}], "eslint no-mixed-spaces-and-tabs": "off"}}