import { Fragment, useEffect, useState } from 'react';
import { Card, Col, Row, Button, Form, Alert } from 'react-bootstrap';
import { useParams, useNavigate } from 'react-router-dom';
import Pageheader from 'layout/layoutcomponent/pageheader.jsx';
import useGeneralStore from 'store/generalStore.js';
import DossierService from 'service/api/dossierService.js';
import toastService from 'utils/toastService.js';
import Loader from 'layout/layoutcomponent/loaders';
import { formatJalaliDate } from 'utils/helper.js';
import DatePicker from 'react-multi-date-picker';
import persian from 'react-date-object/calendars/persian';
import persian_fa from 'react-date-object/locales/persian_fa';
import { formatDate } from '../../utils/helper';

const FinalOpinion = () => {
  const { uuid } = useParams();
  const navigate = useNavigate();
  const { setData } = useGeneralStore();

  const [dossier, setDossier] = useState(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    meetingDate: '',
    meetingMembers: '',
    statements: '',
    finalOpinion: '',
    opinionDocument: null,
  });

  // Fetch dossier data
  useEffect(() => {
    const fetchDossierData = async () => {
      if (!uuid) {
        toastService.error('شناسه پرونده معتبر نیست');
        navigate('/app/inquiry');
        return;
      }

      try {
        setLoading(true);
        const response = await DossierService.getDossier(uuid);
        const dossierData = response?.data?.data;

        if (dossierData) {
          setDossier(dossierData);
          setData({ pageTitle: `نظریه نهایی کارگروه موسسه - پرونده ${dossierData.dossier_code || ''}` });

          // Check if dossier is in correct status
          if (dossierData.dossier_status !== 'final_opinion') {
            toastService.error('این پرونده در مرحله نظریه نهایی نیست');
            navigate('/app/inquiry');
            return;
          }

          // Load existing data if available
          if (dossierData.final_opinion) {
            setFormData({
              meetingDate: dossierData.final_opinion.meetingDate || '',
              meetingMembers: dossierData.final_opinion.meetingMembers || '',
              statements: dossierData.final_opinion.statements || '',
              finalOpinion: dossierData.final_opinion.finalOpinion || '',
              opinionDocument: dossierData.final_opinion.opinionDocument || null,
            });
          }
        } else {
          throw new Error('Dossier data not found');
        }
      } catch (error) {
        console.error('Error fetching dossier:', error);
        toastService.error('خطا در دریافت اطلاعات پرونده');
        navigate('/app/inquiry');
      } finally {
        setLoading(false);
      }
    };

    fetchDossierData();
  }, [uuid, setData, navigate]);

  // Handle form input changes
  const handleInputChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle file upload
  const handleFileUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    try {
      setSubmitting(true);
      const formDataUpload = new FormData();
      formDataUpload.append('document', file);
      formDataUpload.append('document_source', 'final_opinion');
      formDataUpload.append('dossier', uuid);

      const response = await DossierService.uploadDocument(formDataUpload);

      handleInputChange('opinionDocument', response.data);
      toastService.success('فایل با موفقیت آپلود شد');
    } catch (error) {
      console.error('Error uploading file:', error);
      toastService.error('خطا در آپلود فایل');
    } finally {
      setSubmitting(false);
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formData.meetingDate || !formData.meetingMembers || !formData.statements || !formData.finalOpinion) {
      toastService.error('لطفاً تمام فیلدهای الزامی را تکمیل کنید');
      return;
    }

    if (!formData.opinionDocument) {
      toastService.error('بارگذاری صورتجلسه الزامی است');
      return;
    }

    try {
      setSubmitting(true);

      // Submit final opinion data
      const opinionData = {
        final_opinion: {
          session_date: formData.meetingDate,
          session_members: formData.meetingMembers,
          statements: formData.statements,
          opinion: formData.finalOpinion,
        },
      };

      const response = await DossierService.updateFinalOpponionDossier(uuid, opinionData);

      if (response?.data?.status === 'OK') {
        await DossierService.submitDossier(uuid, { description: '---' });
        toastService.success('نظریه نهایی کارگروه موسسه با موفقیت ثبت شد');
        navigate('/app/inquiry');
      }
    } catch (error) {
      console.error('Error submitting final opinion:', error);
      toastService.error('خطا در ثبت نظریه نهایی');
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return <Loader />;
  }

  if (!dossier) {
    return (
      <Fragment>
        <Pageheader title="خطا" />
        <Row>
          <Col lg={12}>
            <Card>
              <Card.Body className="text-center">
                <h4>پرونده یافت نشد</h4>
                <Button variant="primary" onClick={() => navigate('/app/inquiry')}>
                  بازگشت به لیست پرونده‌ها
                </Button>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Fragment>
    );
  }

  // Check if appeal was processed
  const hasAppeal =
    dossier.appeal_request &&
    (dossier.appeal_request.type === 'reportee_appeal' || dossier.appeal_request.type === 'reporter_appeal');

  return (
    <Fragment>
      <Pageheader title={`نظریه نهایی کارگروه موسسه - پرونده ${dossier.dossier_code || ''}`} />

      {/* Action Buttons */}
      <Row className="mb-3">
        <Col lg={12}>
          <div className="d-flex justify-content-between align-items-center">
            <Button variant="outline-secondary" onClick={() => navigate('/app/inquiry')}>
              <i className="fe fe-arrow-right me-2"></i>
              بازگشت به لیست پرونده‌ها
            </Button>

            <Button variant="outline-info" onClick={() => navigate(`/app/dossier/${uuid}/view`)}>
              <i className="fe fe-eye me-2"></i>
              مشاهده کامل پرونده
            </Button>
          </div>
        </Col>
      </Row>

      <Row>
        {/* Dossier Summary */}
        <Col lg={4}>
          <Card className="mb-4">
            <Card.Header>
              <Card.Title className="mb-0">
                <i className="fe fe-info me-2"></i>
                خلاصه پرونده
              </Card.Title>
            </Card.Header>
            <Card.Body>
              <div className="mb-3">
                <strong>شماره پرونده:</strong> {dossier.dossier_code || '---'}
              </div>
              <div className="mb-3">
                <strong>موسسه:</strong> {dossier.institute.title || 'نامشخص'}
              </div>
              <div className="mb-3">
                <strong>گزارش‌دهنده:</strong> {dossier.reporter?.full_name || 'نامشخص'}
              </div>
              <div className="mb-3">
                <strong>گزارش‌شونده:</strong> {dossier.reportee?.full_name || 'نامشخص'}
              </div>

              {/* Show Ministry Opinion Summary if exists */}
              {hasAppeal && dossier.ministry_review && (
                <Alert variant="info" className="mt-3">
                  <i className="fe fe-award me-2"></i>
                  <strong>نظریه کارگروه وزارتی:</strong>
                  <br />
                  <div className="mt-2 p-2 bg-light rounded">
                    {dossier.ministry_review.opinion.substring(0, 100)}
                    {dossier.ministry_review.opinion.length > 100 && '...'}
                  </div>
                  <small className="text-muted">
                    تاریخ جلسه: {formatJalaliDate(dossier.ministry_review.reviewDate)}
                  </small>
                </Alert>
              )}
            </Card.Body>
          </Card>
        </Col>

        {/* Final Opinion Form */}
        <Col lg={8}>
          <Card>
            <Card.Header>
              <Card.Title className="mb-0">
                <i className="fe fe-check-circle me-2"></i>
                نظریه نهایی کارگروه موسسه
              </Card.Title>
            </Card.Header>
            <Card.Body>
              <Alert variant="success">
                <i className="fe fe-info me-2"></i>
                <strong>بخش 3-4: نظریه نهایی کارگروه موسسه پس از تجدیدنظر</strong>
                <br />
                {hasAppeal ? (
                  <>
                    این نظریه نهایی کارگروه موسسه پس از دریافت نظر کارگروه وزارتی در خصوص درخواست تجدیدنظر صادر می‌شود.
                  </>
                ) : (
                  <>
                    از آنجا که درخواست تجدیدنظری وجود نداشته، این نظریه به عنوان نظریه نهایی کارگروه موسسه محسوب می‌شود.
                  </>
                )}
              </Alert>

              <Form onSubmit={handleSubmit}>
                {/* Meeting Date */}
                <Row className="mb-3">
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label>
                        <strong>تاریخ جلسه:</strong>
                        <span className="text-danger ms-1">*</span>
                      </Form.Label>
                      <DatePicker
                        value={formData.meetingDate ? new Date(formData.meetingDate) : null}
                        calendar={persian}
                        locale={persian_fa}
                        calendarPosition="bottom-right"
                        containerClassName={'form-control'}
                        style={{
                          background: 'transparent',
                          width: '100%',
                          boxShadow: 'none!important',
                          outline: 'none',
                          border: 'none',
                        }}
                        onChange={(date) => {
                          const formattedDate = date ? formatDate(new Date(date)) : '';
                          handleInputChange('meetingDate', formattedDate);
                        }}
                        placeholder="تاریخ جلسه را انتخاب کنید"
                      />

                      <Form.Text className="text-muted">
                        تاریخ برگزاری جلسه کارگروه موسسه برای صدور نظریه نهایی
                      </Form.Text>
                    </Form.Group>
                  </Col>
                </Row>

                {/* Meeting Members */}
                <Row className="mb-3">
                  <Col md={12}>
                    <Form.Group>
                      <Form.Label>
                        <strong>اعضای جلسه:</strong>
                        <span className="text-danger ms-1">*</span>
                      </Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={4}
                        value={formData.meetingMembers}
                        onChange={(e) => handleInputChange('meetingMembers', e.target.value)}
                        placeholder="نام و سمت اعضای حاضر در جلسه کارگروه را وارد کنید..."
                        required
                      />
                      <Form.Text className="text-muted">
                        لطفاً نام کامل و سمت هر یک از اعضای حاضر در جلسه را در خطوط جداگانه وارد کنید.
                      </Form.Text>
                    </Form.Group>
                  </Col>
                </Row>

                {/* Statements */}
                <Row className="mb-3">
                  <Col md={12}>
                    <Form.Group>
                      <Form.Label>
                        <strong>اظهارات:</strong>
                        <span className="text-danger ms-1">*</span>
                      </Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={6}
                        value={formData.statements}
                        onChange={(e) => handleInputChange('statements', e.target.value)}
                        placeholder={
                          hasAppeal
                            ? 'اظهارات و بحث‌های مطرح شده در جلسه با در نظر گیری نظر کارگروه وزارتی را وارد کنید...'
                            : 'اظهارات و بحث‌های مطرح شده در جلسه کارگروه را وارد کنید...'
                        }
                        required
                      />
                      <Form.Text className="text-muted">
                        {hasAppeal ? (
                          <>خلاصه‌ای از بحث‌ها و نظرات مطرح شده با در نظر گیری نظریه کارگروه وزارتی.</>
                        ) : (
                          <>خلاصه‌ای از بحث‌ها، نظرات و اظهارات مطرح شده در جلسه کارگروه.</>
                        )}
                      </Form.Text>
                    </Form.Group>
                  </Col>
                </Row>

                {/* Final Opinion */}
                <Row className="mb-3">
                  <Col md={12}>
                    <Form.Group>
                      <Form.Label>
                        <strong>نظریه نهایی:</strong>
                        <span className="text-danger ms-1">*</span>
                      </Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={8}
                        value={formData.finalOpinion}
                        onChange={(e) => handleInputChange('finalOpinion', e.target.value)}
                        placeholder={
                          hasAppeal
                            ? 'نظریه نهایی کارگروه موسسه با در نظر گیری نظر کارگروه وزارتی را وارد کنید...'
                            : 'نظریه نهایی کارگروه موسسه را وارد کنید...'
                        }
                        required
                      />
                      <Form.Text className="text-muted">
                        {hasAppeal ? (
                          <>نظریه و تصمیم نهایی کارگروه موسسه پس از بررسی نظر کارگروه وزارتی.</>
                        ) : (
                          <>نظریه و تصمیم نهایی کارگروه موسسه در خصوص پرونده مورد بررسی.</>
                        )}
                      </Form.Text>
                    </Form.Group>
                  </Col>
                </Row>

                {/* Opinion Document Upload */}
                <Row className="mb-4">
                  <Col md={12}>
                    <Form.Group>
                      <Form.Label>
                        <strong>بارگذاری صورتجلسه:</strong>
                        <span className="text-danger ms-1">*</span>
                      </Form.Label>
                      <Form.Control
                        type="file"
                        accept=".pdf,.doc,.docx"
                        onChange={handleFileUpload}
                        required={!formData.opinionDocument}
                        disabled={submitting}
                      />
                      <Form.Text className="text-muted">
                        فرمت‌های مجاز: PDF, DOC, DOCX - بارگذاری صورتجلسه نظریه نهایی الزامی است
                      </Form.Text>

                      {formData.opinionDocument && (
                        <div className="mt-2">
                          <Alert variant="success" className="py-2">
                            <i className="fe fe-file me-2"></i>
                            فایل صورتجلسه با موفقیت آپلود شد: {formData.opinionDocument.name || 'فایل آپلود شده'}
                          </Alert>
                        </div>
                      )}
                    </Form.Group>
                  </Col>
                </Row>

                {/* Submit Button */}
                <div className="d-flex justify-content-end">
                  <Button type="submit" variant="success" disabled={submitting}>
                    {submitting ? (
                      <>
                        <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        در حال ثبت...
                      </>
                    ) : (
                      <>
                        <i className="fe fe-check me-2"></i>
                        ثبت نظریه نهایی و انتقال به مرحله بعد
                      </>
                    )}
                  </Button>
                </div>

                {/* Process Information */}
                <Alert variant="info" className="mt-3">
                  <i className="fe fe-info me-2"></i>
                  <strong>مرحله بعدی:</strong> پس از تکمیل نظریه نهایی، باید احراز تخلف بررسی شود و در صورت لزوم نظریه
                  به مراجع ذی‌صلاح ابلاغ گردد.
                </Alert>
              </Form>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Fragment>
  );
};

export default FinalOpinion;
