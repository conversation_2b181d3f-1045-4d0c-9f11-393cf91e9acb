{"name": "nowa", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint src --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@coreui/react": "^4.9.2", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@highcharts/map-collection": "^2.3.0", "apexcharts": "^3.52.0", "autoprefixer": "^10.4.15", "axios": "^1.8.4", "boostrap": "^2.0.0", "browser-sync": "^2.29.3", "chart.js": "^4.3.3", "filepond": "^4.30.4", "filepond-plugin-image-exif-orientation": "^1.0.11", "filepond-plugin-image-preview": "^4.6.12", "gulp": "^4.0.2", "gulp-beautify": "^3.0.0", "gulp-cssbeautify": "^3.0.1", "gulp-postcss": "^9.0.1", "gulp-sass": "^5.1.0", "gulp-sourcemaps": "^3.0.0", "highcharts": "^11.4.1", "highcharts-react-official": "^3.2.1", "jalaali-js": "^1.2.7", "moment-jalaali": "^0.10.1", "prop-types": "^15.8.1", "react": "^18.2.0", "react-apexcharts": "^1.4.1", "react-bootstrap": "^2.8.0", "react-circular-progressbar": "^2.1.0", "react-data-table-component": "^7.5.3", "react-data-table-component-extensions": "^1.6.0", "react-datepicker": "^7.3.0", "react-dom": "^18.2.0", "react-filepond": "^7.1.3", "react-iran-map": "^0.1.3", "react-multi-date-picker": "^4.5.2", "react-perfect-scrollbar": "^1.5.8", "react-router-dom": "^6.15.0", "react-select": "^5.8.0", "react-table": "^7.8.0", "react-toastify": "^10.0.5", "react-uploader": "^3.43.0", "sass": "^1.66.1", "styled-components": "^5.3.11", "swiper": "^11.1.12", "uploader": "^3.48.3", "validator": "^13.15.0", "zustand": "^4.5.4"}, "devDependencies": {"@types/react": "^18.2.20", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.4", "eslint": "^8.47.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "prettier": "3.3.3", "vite": "5.4.6"}}