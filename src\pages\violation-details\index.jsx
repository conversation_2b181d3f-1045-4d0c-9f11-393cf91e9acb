import { Fragment, useEffect, useState } from 'react';
import { Card, Col, Row, Button, Form, Alert } from 'react-bootstrap';
import { useParams, useNavigate } from 'react-router-dom';
import Select from 'react-select';
import Pageheader from 'layout/layoutcomponent/pageheader.jsx';
import useGeneralStore from 'store/generalStore.js';
import DossierService from 'service/api/dossierService.js';
import toastService from 'utils/toastService.js';
import Loader from 'layout/layoutcomponent/loaders';
import { formatJalaliDate, convertToPersianNumbers } from 'utils/helper.js';
import { FORM_OPTIONS, getViolationYears, getViolationCounts } from 'utils/formOptions.js';

const ViolationDetails = () => {
  const { uuid } = useParams();
  const navigate = useNavigate();
  const { setData } = useGeneralStore();

  const [dossier, setDossier] = useState(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    // احراز تخلف
    violationVerification: '', // احراز یا عدم احراز

    // فیلدهای مشروط (فقط در صورت احراز)
    violationYear: '', // سال وقوع تخلف
    violationType: '', // نوع تخلف
    violationTypeOther: '', // سایر (در صورت انتخاب سایر)
    violationCount: '', // تعداد موارد تخلف محرزشده
    benefitFromViolation: '', // انتفاع یا عدم انتفاع

    // نوع انتفاع (مشروط بر انتخاب انتفاع)
    benefitTypeFaculty: [], // نوع انتفاع هیات علمی
    benefitTypeStudent: [], // نوع انتفاع دانشجو
    benefitTypeEmployee: [], // نوع انتفاع کارمند
    benefitTypeResearcher: [], // نوع انتفاع پژوهشگر آزاد
    benefitOtherDescription: '', // توضیح سایر انتفاع

    // سایر فیلدها
    violationStage: '', // تخلف در کدام مرحله پژوهش
    violationReasons: '', // دلایل بروز تخلف
    violationPattern: '', // موردی یا سازمان‌یافته
    violationEffects: [], // اثرات تخلف پژوهشی

    documents: [],
  });

  // Get options from formOptions
  const verificationOptions = FORM_OPTIONS.violationVerification;
  const violationYears = getViolationYears();
  const violationTypes = FORM_OPTIONS.violationTypes;
  const violationCounts = getViolationCounts();
  const benefitOptions = FORM_OPTIONS.benefitFromViolation;
  const facultyBenefitTypes = FORM_OPTIONS.facultyBenefitTypes;
  const studentBenefitTypes = FORM_OPTIONS.studentBenefitTypes;
  const employeeBenefitTypes = FORM_OPTIONS.employeeBenefitTypes;
  const researcherBenefitTypes = FORM_OPTIONS.researcherBenefitTypes;
  const researchStages = FORM_OPTIONS.violationResearchStages;
  const violationPatterns = FORM_OPTIONS.violationPatterns;
  const violationEffects = FORM_OPTIONS.violationEffects;

  // Fetch dossier data
  useEffect(() => {
    const fetchDossierData = async () => {
      if (!uuid) {
        toastService.error('شناسه پرونده معتبر نیست');
        navigate('/app/inquiry');
        return;
      }

      try {
        setLoading(true);
        const response = await DossierService.getDossier(uuid);
        const dossierData = response?.data?.data;

        if (dossierData) {
          setDossier(dossierData);
          setData({ pageTitle: `احراز تخلف - پرونده ${dossierData.dossier_code || ''}` });

          // Load existing data if available
          if (dossierData.violation_details) {
            setFormData({
              violationVerification: dossierData.violation_details.violationVerification || '',
              violationYear: dossierData.violation_details.violationYear || '',
              violationType: dossierData.violation_details.violationType || '',
              violationTypeOther: dossierData.violation_details.violationTypeOther || '',
              violationCount: dossierData.violation_details.violationCount || '',
              benefitFromViolation: dossierData.violation_details.benefitFromViolation || '',
              benefitTypeFaculty: dossierData.violation_details.benefitTypeFaculty || [],
              benefitTypeStudent: dossierData.violation_details.benefitTypeStudent || [],
              benefitTypeEmployee: dossierData.violation_details.benefitTypeEmployee || [],
              benefitTypeResearcher: dossierData.violation_details.benefitTypeResearcher || [],
              benefitOtherDescription: dossierData.violation_details.benefitOtherDescription || '',
              violationStage: dossierData.violation_details.violationStage || '',
              violationReasons: dossierData.violation_details.violationReasons || '',
              violationPattern: dossierData.violation_details.violationPattern || '',
              violationEffects: dossierData.violation_details.violationEffects || [],
              documents: dossierData.violation_details.documents || [],
            });
          }
        } else {
          throw new Error('Dossier data not found');
        }
      } catch (error) {
        console.error('Error fetching dossier:', error);
        toastService.error('خطا در دریافت اطلاعات پرونده');
        navigate('/app/inquiry');
      } finally {
        setLoading(false);
      }
    };

    fetchDossierData();
  }, [uuid, setData, navigate]);

  // Handle form input changes
  const handleInputChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle checkbox changes for arrays
  const handleCheckboxChange = (field, value, checked) => {
    setFormData((prev) => {
      const currentArray = prev[field] || [];
      if (checked) {
        return {
          ...prev,
          [field]: [...currentArray, value],
        };
      } else {
        return {
          ...prev,
          [field]: currentArray.filter((item) => item !== value),
        };
      }
    });
  };

  // Handle file upload
  const handleFileUpload = async (event) => {
    const files = Array.from(event.target.files);
    if (files.length === 0) return;

    try {
      setSubmitting(true);
      const uploadPromises = files.map(async (file) => {
        const formDataUpload = new FormData();
        formDataUpload.append('document', file);
        formDataUpload.append('document_source', 'violation_details');
        formDataUpload.append('dossier', uuid);

        const response = await DossierService.uploadDocument(formDataUpload);
        return response.data.data;
      });

      const uploadedDocs = await Promise.all(uploadPromises);

      setFormData((prev) => ({
        ...prev,
        documents: [...prev.documents, ...uploadedDocs],
      }));

      toastService.success('فایل‌ها با موفقیت آپلود شدند');
    } catch (error) {
      console.error('Error uploading files:', error);
      toastService.error('خطا در آپلود فایل‌ها');
    } finally {
      setSubmitting(false);
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Basic validation
    if (!formData.violationVerification) {
      toastService.error('لطفاً وضعیت احراز تخلف را مشخص کنید');
      return;
    }

    // Additional validation if violation is confirmed
    if (formData.violationVerification === 'confirmed') {
      if (
        !formData.violationYear ||
        !formData.violationType ||
        !formData.violationCount ||
        !formData.benefitFromViolation ||
        !formData.violationStage ||
        !formData.violationReasons ||
        !formData.violationPattern
      ) {
        toastService.error('لطفاً تمام فیلدهای الزامی را تکمیل کنید');
        return;
      }

      // Validate "other" type description
      if (formData.violationType === 'other' && !formData.violationTypeOther) {
        toastService.error('لطفاً توضیح نوع تخلف "سایر" را وارد کنید');
        return;
      }

      // Validate benefit types if benefit is selected
      if (formData.benefitFromViolation === 'benefited') {
        const hasBenefitType =
          formData.benefitTypeFaculty.length > 0 ||
          formData.benefitTypeStudent.length > 0 ||
          formData.benefitTypeEmployee.length > 0 ||
          formData.benefitTypeResearcher.length > 0;
        if (!hasBenefitType) {
          toastService.error('لطفاً حداقل یک نوع انتفاع را انتخاب کنید');
          return;
        }
      }

      // Validate violation effects
      if (formData.violationEffects.length === 0) {
        toastService.error('لطفاً حداقل یک اثر تخلف پژوهشی را انتخاب کنید');
        return;
      }
    }

    try {
      setSubmitting(true);

      // Submit violation details data
      let violationData = {};

      if (formData.violationVerification === 'confirmed') {
        violationData = {
          violation_details: {
            violation_status: formData.violationVerification,
            violation_year: formData.violationYear || 0,
            violation_type: formData.violationType,
            other_violation_type: formData.violationTypeOther || '',
            violation_count: formData.violationCount || 1,
            benefit_status: formData.benefitFromViolation,
            faculty_benefit: formData.benefitTypeFaculty.length > 0 ? formData.benefitTypeFaculty[0] : '',
            faculty_benefit_other: formData.benefitTypeFaculty.includes('other')
              ? formData.benefitOtherDescription
              : '',
            student_benefit: formData.benefitTypeStudent.length > 0 ? formData.benefitTypeStudent[0] : '',
            student_benefit_other: formData.benefitTypeStudent.includes('other')
              ? formData.benefitOtherDescription
              : '',
            employee_benefit: formData.benefitTypeEmployee.length > 0 ? formData.benefitTypeEmployee[0] : '',
            employee_benefit_other: formData.benefitTypeEmployee.includes('other')
              ? formData.benefitOtherDescription
              : '',
            independent_researcher_benefit:
              formData.benefitTypeResearcher.length > 0 ? formData.benefitTypeResearcher[0] : '',
            research_stage: formData.violationStage,
            violation_reasons: formData.violationReasons,
            violation_nature: formData.violationPattern,
            violation_impact: formData.violationEffects,
          },
        };
      } else {
        violationData = {
          violation_details: {
            violation_status: formData.violationVerification,
          },
        };
      }

      const response = await DossierService.updateViolationDetailsDossier(uuid, violationData);

      if (response?.data?.status === 'OK') {
        await DossierService.submitDossier(uuid, { description: '---' });
        toastService.success('اطلاعات احراز تخلف با موفقیت ثبت شد');
        navigate('/app/inquiry');
      }
    } catch (error) {
      console.error('Error submitting violation details:', error);
      toastService.error('خطا در ثبت اطلاعات احراز تخلف');
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return <Loader />;
  }

  if (!dossier) {
    return (
      <Fragment>
        <Pageheader title="خطا" />
        <Row>
          <Col lg={12}>
            <Card>
              <Card.Body className="text-center">
                <h4>پرونده یافت نشد</h4>
                <Button variant="primary" onClick={() => navigate('/app/inquiry')}>
                  بازگشت به لیست پرونده‌ها
                </Button>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Fragment>
    );
  }

  return (
    <Fragment>
      <Pageheader title={`احراز تخلف - پرونده ${dossier.dossier_code || ''}`} />

      {/* Action Buttons */}
      <Row className="mb-3">
        <Col lg={12}>
          <div className="d-flex justify-content-between align-items-center">
            <Button variant="outline-secondary" onClick={() => navigate('/app/inquiry')}>
              <i className="fe fe-arrow-right me-2"></i>
              بازگشت به لیست پرونده‌ها
            </Button>

            <Button variant="outline-info" onClick={() => navigate(`/app/dossier/${uuid}/view`)}>
              <i className="fe fe-eye me-2"></i>
              مشاهده کامل پرونده
            </Button>
          </div>
        </Col>
      </Row>

      <Row>
        {/* Dossier Summary */}
        <Col lg={4}>
          <Card className="mb-4">
            <Card.Header>
              <Card.Title className="mb-0">
                <i className="fe fe-info me-2"></i>
                خلاصه پرونده
              </Card.Title>
            </Card.Header>
            <Card.Body>
              <div className="mb-3">
                <strong>شماره پرونده:</strong> {dossier.dossier_code || '---'}
              </div>
              <div className="mb-3">
                <strong>موسسه:</strong> {dossier.institute.title || 'نامشخص'}
              </div>
              <div className="mb-3">
                <strong>گزارش‌دهنده:</strong> {dossier.reporter?.full_name || 'نامشخص'}
              </div>
              <div className="mb-3">
                <strong>گزارش‌شونده:</strong> {dossier.reportee?.full_name || 'نامشخص'}
              </div>
              <div className="mb-3">
                <strong>تاریخ ایجاد:</strong> {formatJalaliDate(dossier.created_at)}
              </div>
            </Card.Body>
          </Card>
        </Col>

        {/* Violation Details Form */}
        <Col lg={8}>
          <Card>
            <Card.Header>
              <Card.Title className="mb-0">
                <i className="fe fe-check-circle me-2"></i>
                احراز تخلف
              </Card.Title>
            </Card.Header>
            <Card.Body>
              <Alert variant="info">
                <i className="fe fe-info me-2"></i>
                در این بخش، کارشناس پس از بررسی پرونده، وضعیت احراز تخلف و جزئیات مربوطه را ثبت می‌کند.
              </Alert>

              <Form onSubmit={handleSubmit}>
                {/* احراز تخلف */}
                <Row className="mb-4">
                  <Col md={12}>
                    <Form.Group>
                      <Form.Label>
                        <strong>احراز تخلف:</strong>
                        <span className="text-danger ms-1">*</span>
                      </Form.Label>
                      <div className="mt-2">
                        {verificationOptions.map((option) => (
                          <Form.Check
                            key={option.value}
                            type="radio"
                            id={`verification-${option.value}`}
                            name="violationVerification"
                            label={option.label}
                            value={option.value}
                            checked={formData.violationVerification === option.value}
                            onChange={(e) => handleInputChange('violationVerification', e.target.value)}
                            className="mb-2"
                          />
                        ))}
                      </div>
                    </Form.Group>
                  </Col>
                </Row>

                {/* فیلدهای مشروط - فقط در صورت احراز تخلف */}
                {formData.violationVerification === 'confirmed' && (
                  <>
                    {/* سال وقوع تخلف */}
                    <Row className="mb-3">
                      <Col md={6}>
                        <Form.Group>
                          <Form.Label>
                            <strong>سال وقوع تخلف:</strong>
                            <span className="text-danger ms-1">*</span>
                          </Form.Label>
                          <Select
                            options={violationYears}
                            placeholder="انتخاب کنید..."
                            classNamePrefix="Select2"
                            isSearchable
                            value={violationYears.find((year) => year.value === formData.violationYear) || null}
                            onChange={(selectedOption) =>
                              handleInputChange('violationYear', selectedOption?.value || '')
                            }
                            formatOptionLabel={(option) => convertToPersianNumbers(option.label)}
                          />
                        </Form.Group>
                      </Col>

                      {/* نوع تخلف */}
                      <Col md={6}>
                        <Form.Group>
                          <Form.Label>
                            <strong>نوع تخلف:</strong>
                            <span className="text-danger ms-1">*</span>
                          </Form.Label>
                          <Select
                            options={violationTypes}
                            placeholder="انتخاب کنید..."
                            classNamePrefix="Select2"
                            isSearchable
                            value={violationTypes.find((type) => type.value === formData.violationType) || null}
                            onChange={(selectedOption) =>
                              handleInputChange('violationType', selectedOption?.value || '')
                            }
                          />
                        </Form.Group>
                      </Col>
                    </Row>

                    {/* توضیح سایر - در صورت انتخاب سایر */}
                    {formData.violationType === 'other' && (
                      <Row className="mb-3">
                        <Col md={12}>
                          <Form.Group>
                            <Form.Label>
                              <strong>توضیح نوع تخلف:</strong>
                              <span className="text-danger ms-1">*</span>
                            </Form.Label>
                            <Form.Control
                              type="text"
                              value={formData.violationTypeOther}
                              onChange={(e) => handleInputChange('violationTypeOther', e.target.value)}
                              placeholder="نوع تخلف را توضیح دهید..."
                              required
                            />
                          </Form.Group>
                        </Col>
                      </Row>
                    )}

                    {/* تعداد موارد تخلف */}
                    <Row className="mb-3">
                      <Col md={6}>
                        <Form.Group>
                          <Form.Label>
                            <strong>تعداد موارد تخلف محرزشده:</strong>
                            <span className="text-danger ms-1">*</span>
                          </Form.Label>
                          <Select
                            options={violationCounts}
                            placeholder="انتخاب کنید..."
                            classNamePrefix="Select2"
                            isSearchable
                            value={violationCounts.find((count) => count.value === formData.violationCount) || null}
                            onChange={(selectedOption) =>
                              handleInputChange('violationCount', selectedOption?.value || '')
                            }
                            formatOptionLabel={(option) => convertToPersianNumbers(option.label)}
                          />
                        </Form.Group>
                      </Col>

                      {/* انتفاع گزارش شونده */}
                      <Col md={6}>
                        <Form.Group>
                          <Form.Label>
                            <strong>انتفاع گزارش شونده از تخلف:</strong>
                            <span className="text-danger ms-1">*</span>
                          </Form.Label>
                          <div className="mt-2">
                            {benefitOptions.map((option) => (
                              <Form.Check
                                key={option.value}
                                type="radio"
                                id={`benefit-${option.value}`}
                                name="benefitFromViolation"
                                label={option.label}
                                value={option.value}
                                checked={formData.benefitFromViolation === option.value}
                                onChange={(e) => handleInputChange('benefitFromViolation', e.target.value)}
                                className="mb-2"
                              />
                            ))}
                          </div>
                        </Form.Group>
                      </Col>
                    </Row>
                  </>
                )}

                {/* انواع انتفاع - در صورت انتخاب انتفاع */}
                {formData.benefitFromViolation === 'benefited' && (
                  <Row className="mb-4">
                    <Col md={12}>
                      <Card className="border-info">
                        <Card.Header className="bg-light">
                          <Card.Title className="mb-0 h6">
                            <i className="fe fe-award me-2"></i>
                            انواع انتفاع
                          </Card.Title>
                        </Card.Header>
                        <Card.Body>
                          {/* انتفاع هیات علمی */}
                          <Row className="mb-3">
                            <Col md={6}>
                              <Form.Label>
                                <strong>نوع انتفاع (هیات علمی):</strong>
                              </Form.Label>
                              {facultyBenefitTypes.map((type, index) => (
                                <Form.Check
                                  key={index}
                                  type="checkbox"
                                  id={`faculty-${index}`}
                                  label={type.label}
                                  checked={formData.benefitTypeFaculty.includes(type.value)}
                                  onChange={(e) =>
                                    handleCheckboxChange('benefitTypeFaculty', type.value, e.target.checked)
                                  }
                                  className="mb-1"
                                />
                              ))}
                            </Col>

                            {/* انتفاع دانشجو */}
                            <Col md={6}>
                              <Form.Label>
                                <strong>نوع انتفاع (دانشجو):</strong>
                              </Form.Label>
                              {studentBenefitTypes.map((type, index) => (
                                <Form.Check
                                  key={index}
                                  type="checkbox"
                                  id={`student-${index}`}
                                  label={type.label}
                                  checked={formData.benefitTypeStudent.includes(type.value)}
                                  onChange={(e) =>
                                    handleCheckboxChange('benefitTypeStudent', type.value, e.target.checked)
                                  }
                                  className="mb-1"
                                />
                              ))}
                            </Col>
                          </Row>

                          <Row className="mb-3">
                            {/* انتفاع کارمند */}
                            <Col md={6}>
                              <Form.Label>
                                <strong>نوع انتفاع (کارمند):</strong>
                              </Form.Label>
                              {employeeBenefitTypes.map((type, index) => (
                                <Form.Check
                                  key={index}
                                  type="checkbox"
                                  id={`employee-${index}`}
                                  label={type.label}
                                  checked={formData.benefitTypeEmployee.includes(type.value)}
                                  onChange={(e) =>
                                    handleCheckboxChange('benefitTypeEmployee', type.value, e.target.checked)
                                  }
                                  className="mb-1"
                                />
                              ))}
                            </Col>

                            {/* انتفاع پژوهشگر آزاد */}
                            <Col md={6}>
                              <Form.Label>
                                <strong>نوع انتفاع (پژوهشگر آزاد):</strong>
                              </Form.Label>
                              {researcherBenefitTypes.map((type, index) => (
                                <Form.Check
                                  key={index}
                                  type="checkbox"
                                  id={`researcher-${index}`}
                                  label={type.label}
                                  checked={formData.benefitTypeResearcher.includes(type.value)}
                                  onChange={(e) =>
                                    handleCheckboxChange('benefitTypeResearcher', type.value, e.target.checked)
                                  }
                                  className="mb-1"
                                />
                              ))}
                            </Col>
                          </Row>

                          {/* توضیح سایر انتفاع */}
                          {(formData.benefitTypeFaculty.includes('other') ||
                            formData.benefitTypeStudent.includes('other') ||
                            formData.benefitTypeEmployee.includes('other') ||
                            formData.benefitTypeResearcher.includes('other')) && (
                            <Row>
                              <Col md={12}>
                                <Form.Group>
                                  <Form.Label>
                                    <strong>توضیح سایر انتفاع:</strong>
                                    <span className="text-danger ms-1">*</span>
                                  </Form.Label>
                                  <Form.Control
                                    as="textarea"
                                    rows={3}
                                    value={formData.benefitOtherDescription}
                                    onChange={(e) => handleInputChange('benefitOtherDescription', e.target.value)}
                                    placeholder="توضیح دهید..."
                                    required
                                  />
                                </Form.Group>
                              </Col>
                            </Row>
                          )}
                        </Card.Body>
                      </Card>
                    </Col>
                  </Row>
                )}

                {/* مرحله پژوهش و سایر فیلدها - ادامه فیلدهای مشروط */}
                {formData.violationVerification === 'confirmed' && (
                  <>
                    {/* مرحله پژوهش */}
                    <Row className="mb-3">
                      <Col md={12}>
                        <Form.Group>
                          <Form.Label>
                            <strong>تخلف پژوهشی در کدام مرحله پژوهش:</strong>
                            <span className="text-danger ms-1">*</span>
                          </Form.Label>
                          <div className="mt-2">
                            {researchStages.map((stage, index) => (
                              <Form.Check
                                key={index}
                                type="radio"
                                id={`stage-${index}`}
                                name="violationStage"
                                label={stage.label}
                                value={stage.value}
                                checked={formData.violationStage === stage.value}
                                onChange={(e) => handleInputChange('violationStage', e.target.value)}
                                className="mb-2"
                              />
                            ))}
                          </div>
                        </Form.Group>
                      </Col>
                    </Row>

                    {/* دلایل بروز تخلف */}
                    <Row className="mb-3">
                      <Col md={12}>
                        <Form.Group>
                          <Form.Label>
                            <strong>دلایل بروز تخلف:</strong>
                            <span className="text-danger ms-1">*</span>
                          </Form.Label>
                          <Form.Control
                            as="textarea"
                            rows={4}
                            value={formData.violationReasons}
                            onChange={(e) => handleInputChange('violationReasons', e.target.value)}
                            placeholder="با توجه به محتوای مستندات و اظهارات، دلایل بروز تخلف را شرح دهید..."
                            required
                          />
                        </Form.Group>
                      </Col>
                    </Row>

                    {/* الگوی تخلف */}
                    <Row className="mb-3">
                      <Col md={12}>
                        <Form.Group>
                          <Form.Label>
                            <strong>ارتکاب موردی یا سازمان‌یافته تخلف:</strong>
                            <span className="text-danger ms-1">*</span>
                          </Form.Label>
                          <div className="mt-2">
                            {violationPatterns.map((pattern) => (
                              <Form.Check
                                key={pattern.value}
                                type="radio"
                                id={`pattern-${pattern.value}`}
                                name="violationPattern"
                                label={pattern.label}
                                value={pattern.value}
                                checked={formData.violationPattern === pattern.value}
                                onChange={(e) => handleInputChange('violationPattern', e.target.value)}
                                className="mb-2"
                              />
                            ))}
                          </div>
                        </Form.Group>
                      </Col>
                    </Row>

                    {/* اثرات تخلف پژوهشی */}
                    <Row className="mb-4">
                      <Col md={12}>
                        <Form.Group>
                          <Form.Label>
                            <strong>اثرات تخلف پژوهشی:</strong>
                            <span className="text-danger ms-1">*</span>
                          </Form.Label>
                          <div className="mt-2">
                            <Row>
                              {violationEffects.map((effect, index) => (
                                <Col md={6} key={index} className="mb-2">
                                  <Form.Check
                                    type="checkbox"
                                    id={`effect-${index}`}
                                    label={effect.label}
                                    checked={formData.violationEffects.includes(effect.value)}
                                    onChange={(e) =>
                                      handleCheckboxChange('violationEffects', effect.value, e.target.checked)
                                    }
                                  />
                                </Col>
                              ))}
                            </Row>
                          </div>
                        </Form.Group>
                      </Col>
                    </Row>
                  </>
                )}

                <div className="d-flex justify-content-end">
                  <Button type="submit" variant="success" disabled={submitting}>
                    {submitting ? (
                      <>
                        <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        در حال ثبت...
                      </>
                    ) : (
                      <>
                        <i className="fe fe-check me-2"></i>
                        ثبت احراز تخلف
                      </>
                    )}
                  </Button>
                </div>
              </Form>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Fragment>
  );
};

export default ViolationDetails;
