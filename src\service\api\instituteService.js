import useFetch from '../index';
import { jsonToQueryString } from 'utils/helper.js';

class InstituteService {
  addInstitute(data) {
    return useFetch.post('/api/management/v1/institute/', data);
  }

  getList(query) {
    return useFetch.get('/api/management/v1/institute/list/?' + jsonToQueryString(query));
  }

  getMyInstitute(query) {
    return useFetch.get('/api/v1/institute/?' + jsonToQueryString(query));
  }

  updateInstitute(uuid, data) {
    if (!uuid) return false;
    return useFetch.patch(`/api/management/v1/institute/${uuid}/update/`, data);
  }

  deleteInstitute(uuid) {
    if (!uuid) return false;
    return useFetch.delete(`/api/management/v1/institute/${uuid}/`);
  }

  deleteInstituteUser(uuid) {
    if (!uuid) return false;
    return useFetch.delete(`/api/management/v1/institute/${uuid}/member/`);
  }
}
export default new InstituteService();
