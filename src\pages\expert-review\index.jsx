import { Fragment, useEffect, useState } from 'react';
import { Card, Col, Row, Button, Form, Alert } from 'react-bootstrap';
import { useParams, useNavigate } from 'react-router-dom';
import Pageheader from 'layout/layoutcomponent/pageheader.jsx';
import useGeneralStore from 'store/generalStore.js';
import DossierService from 'service/api/dossierService.js';
import toastService from 'utils/toastService.js';
import Loader from 'layout/layoutcomponent/loaders';
import { formatJalaliDate } from 'utils/helper.js';

const ExpertReview = () => {
  const { uuid } = useParams();
  const navigate = useNavigate();
  const { setData } = useGeneralStore();

  const [dossier, setDossier] = useState(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    expertOpinion: '',
    documents: [],
  });

  // Fetch dossier data
  useEffect(() => {
    const fetchDossierData = async () => {
      if (!uuid) {
        toastService.error('شناسه پرونده معتبر نیست');
        navigate('/app/inquiry');
        return;
      }

      try {
        setLoading(true);
        const response = await DossierService.getDossier(uuid);
        const dossierData = response?.data?.data;

        if (dossierData) {
          setDossier(dossierData);
          setData({ pageTitle: `کارشناسی پرونده ${dossierData.dossier_code || ''}` });

          // Check if dossier is in correct status
          if (dossierData.dossier_status !== 'expert_review') {
            toastService.error('این پرونده در مرحله کارشناسی نیست');
            navigate('/app/inquiry');
            return;
          }

          // Load existing expert review data if available
          if (dossierData.expert_review) {
            setFormData({
              expertOpinion: dossierData.expert_review.opinion || '',
              documents: dossierData.expert_review.documents || [],
            });
          }
        } else {
          throw new Error('Dossier data not found');
        }
      } catch (error) {
        console.error('Error fetching dossier:', error);
        toastService.error('خطا در دریافت اطلاعات پرونده');
        navigate('/app/inquiry');
      } finally {
        setLoading(false);
      }
    };

    fetchDossierData();
  }, [uuid, setData, navigate]);

  // Handle form input changes
  const handleInputChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle file upload
  const handleFileUpload = async (event) => {
    const files = Array.from(event.target.files);
    if (files.length === 0) return;

    try {
      setSubmitting(true);
      for (const file of files) {
        try {
          const formData = new FormData();
          formData.append('document', file);
          formData.append('document_source', 'expert');
          formData.append('dossier', uuid);

          await DossierService.uploadDocument(formData);
          toastService.success(`فایل ${file.name} با موفقیت آپلود شد`);

          // Add to local state for display
          setFormData((prev) => ({
            ...prev,
            documents: [...prev.documents, file],
          }));
        } catch (error) {
          console.error('Error uploading file:', error);
          toastService.error(`خطا در آپلود فایل ${file.name}`);
        }
      }
    } catch (error) {
      console.error('Error uploading files:', error);
      toastService.error('خطا در آپلود فایل‌ها');
    } finally {
      setSubmitting(false);
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formData.expertOpinion.trim()) {
      toastService.error('لطفاً نظر کارشناسی را وارد کنید');
      return;
    }

    try {
      setSubmitting(true);

      // Submit expert review data
      const reviewData = {
        expert_description: formData.expertOpinion,
      };

      const response = await DossierService.updateExpertDossier(uuid, reviewData);

      if (response?.data?.status === 'OK') {
        await DossierService.submitDossier(uuid, { description: '---' });
        toastService.success('نظر کارشناسی با موفقیت ثبت شد');
        navigate('/app/inquiry');
      }
    } catch (error) {
      console.error('Error submitting expert review:', error);
      toastService.error('خطا در ثبت نظر کارشناسی');
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return <Loader />;
  }

  if (!dossier) {
    return (
      <Fragment>
        <Pageheader title="خطا" />
        <Row>
          <Col lg={12}>
            <Card>
              <Card.Body className="text-center">
                <h4>پرونده یافت نشد</h4>
                <Button variant="primary" onClick={() => navigate('/app/inquiry')}>
                  بازگشت به لیست پرونده‌ها
                </Button>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Fragment>
    );
  }

  return (
    <Fragment>
      <Pageheader title={`کارشناسی پرونده ${dossier.dossier_code || ''}`} />

      {/* Action Buttons */}
      <Row className="mb-3">
        <Col lg={12}>
          <div className="d-flex justify-content-between align-items-center">
            <Button variant="outline-secondary" onClick={() => navigate('/app/inquiry')}>
              <i className="fe fe-arrow-right me-2"></i>
              بازگشت به لیست پرونده‌ها
            </Button>

            <Button variant="outline-info" onClick={() => navigate(`/app/dossier/${uuid}/view`)}>
              <i className="fe fe-eye me-2"></i>
              مشاهده کامل پرونده
            </Button>
          </div>
        </Col>
      </Row>

      <Row>
        {/* Dossier Summary */}
        <Col lg={4}>
          <Card className="mb-4">
            <Card.Header>
              <Card.Title className="mb-0">
                <i className="fe fe-info me-2"></i>
                خلاصه پرونده
              </Card.Title>
            </Card.Header>
            <Card.Body>
              <div className="mb-3">
                <strong>شماره پرونده:</strong> {dossier.dossier_code || '---'}
              </div>
              <div className="mb-3">
                <strong>موسسه:</strong> {dossier.institute.title || 'نامشخص'}
              </div>
              <div className="mb-3">
                <strong>گزارش‌دهنده:</strong> {dossier.reporter?.full_name || 'نامشخص'}
              </div>
              <div className="mb-3">
                <strong>گزارش‌شونده:</strong> {dossier.reportee?.full_name || 'نامشخص'}
              </div>
              <div className="mb-3">
                <strong>تاریخ ایجاد:</strong> {formatJalaliDate(dossier.created_at)}
              </div>
            </Card.Body>
          </Card>
        </Col>

        {/* Expert Review Form */}
        <Col lg={8}>
          <Card>
            <Card.Header>
              <Card.Title className="mb-0">
                <i className="fe fe-search me-2"></i>
                نظر کارشناسی
              </Card.Title>
            </Card.Header>
            <Card.Body>
              <Alert variant="info">
                <i className="fe fe-info me-2"></i>
                در این قسمت، کارشناس می‌تواند جمع‌بندی نظرات حاصل از بررسی کارشناسان/داوران از تخلف را ثبت کند؛ و نیز
                امکان بارگذاری مستندات کارشناسان/داوران را دارد.
              </Alert>

              <Form onSubmit={handleSubmit}>
                <Form.Group className="mb-4">
                  <Form.Label>
                    <strong>نظر کارشناسی:</strong>
                  </Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={8}
                    value={formData.expertOpinion}
                    onChange={(e) => handleInputChange('expertOpinion', e.target.value)}
                    placeholder="جمع‌بندی نظرات کارشناسان و داوران را در این قسمت وارد کنید..."
                    // required
                  />
                </Form.Group>

                <Form.Group className="mb-4">
                  <Form.Label>
                    <strong>بارگذاری مستندات کارشناسان/داوران:</strong>
                  </Form.Label>
                  <Form.Control
                    type="file"
                    multiple
                    accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                    onChange={handleFileUpload}
                    disabled={submitting}
                  />
                  <Form.Text className="text-muted">فرمت‌های مجاز: PDF, DOC, DOCX, JPG, PNG</Form.Text>
                </Form.Group>

                {/* Uploaded Documents */}
                {formData.documents.length > 0 && (
                  <div className="mb-4">
                    <strong>فایل‌های آپلود شده:</strong>
                    <ul className="list-unstyled mt-2">
                      {formData.documents.map((doc, index) => (
                        <li key={index} className="d-flex align-items-center mb-2">
                          <i className="fe fe-file me-2"></i>
                          <span>{doc.name || `فایل ${index + 1}`}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                <div className="d-flex justify-content-end">
                  <Button type="submit" variant="success" disabled={submitting}>
                    {submitting ? (
                      <>
                        <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        در حال ثبت...
                      </>
                    ) : (
                      <>
                        <i className="fe fe-check me-2"></i>
                        ثبت نظر کارشناسی و انتقال به مرحله بعد
                      </>
                    )}
                  </Button>
                </div>
              </Form>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Fragment>
  );
};

export default ExpertReview;
