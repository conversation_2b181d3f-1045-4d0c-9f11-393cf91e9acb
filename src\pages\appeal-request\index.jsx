import { Fragment, useEffect, useState } from 'react';
import { Card, Col, Row, Button, Form, Alert } from 'react-bootstrap';
import { useParams, useNavigate } from 'react-router-dom';
import Pageheader from 'layout/layoutcomponent/pageheader.jsx';
import useGeneralStore from 'store/generalStore.js';
import DossierService from 'service/api/dossierService.js';
import toastService from 'utils/toastService.js';
import Loader from 'layout/layoutcomponent/loaders';
import { formatJalaliDate } from 'utils/helper.js';
import DatePicker from 'react-multi-date-picker';
import persian from 'react-date-object/calendars/persian';
import persian_fa from 'react-date-object/locales/persian_fa';
import { formatDate } from '../../utils/helper';

const AppealRequest = () => {
  const { uuid } = useParams();
  const navigate = useNavigate();
  const { setData } = useGeneralStore();

  const [dossier, setDossier] = useState(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [formData, setFormData] = useState({
    type: 'no_review', // 'reportee_review', 'reporter_review', 'no_review'
    requestDate: '',
    documents: [],
  });

  // Fetch dossier data
  useEffect(() => {
    const fetchDossierData = async () => {
      if (!uuid) {
        toastService.error('شناسه پرونده معتبر نیست');
        navigate('/app/inquiry');
        return;
      }

      try {
        setLoading(true);
        const response = await DossierService.getDossier(uuid);
        const dossierData = response?.data?.data;

        if (dossierData) {
          setDossier(dossierData);
          setData({ pageTitle: `درخواست تجدیدنظر - پرونده ${dossierData.dossier_code || ''}` });

          // Check if dossier is in correct status
          if (dossierData.dossier_status !== 'appeal_request') {
            toastService.error('این پرونده در مرحله درخواست تجدیدنظر نیست');
            navigate('/app/inquiry');
            return;
          }

          // Load existing data if available
          if (dossierData.appeal_request) {
            setFormData({
              type: dossierData.appeal_request.type || 'no_appeal',
              requestDate: dossierData.appeal_request.requestDate || '',
              documents: dossierData.appeal_request.documents || [],
            });
          }
        } else {
          throw new Error('Dossier data not found');
        }
      } catch (error) {
        console.error('Error fetching dossier:', error);
        toastService.error('خطا در دریافت اطلاعات پرونده');
        navigate('/app/inquiry');
      } finally {
        setLoading(false);
      }
    };

    fetchDossierData();
  }, [uuid, setData, navigate]);

  // Handle form input changes
  const handleInputChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle appeal type change
  const handleAppealTypeChange = (type) => {
    handleInputChange('type', type);

    // Reset related fields when changing type
    if (type === 'no_review') {
      handleInputChange('requestDate', '');
      handleInputChange('documents', []);
    }
  };

  // Handle file upload
  const handleFileUpload = async (event) => {
    const files = Array.from(event.target.files);
    if (files.length === 0) return;

    try {
      setUploading(true);
      for (const file of files) {
        try {
          const formData = new FormData();
          formData.append('document', file);
          formData.append('document_source', 'appeal_request');
          formData.append('dossier', uuid);

          await DossierService.uploadDocument(formData);
          toastService.success(`فایل ${file.name} با موفقیت آپلود شد`);

          // Add to local state for display
          setFormData((prev) => ({
            ...prev,
            documents: [...prev.documents, file],
          }));
        } catch (error) {
          console.error('Error uploading file:', error);
          toastService.error(`خطا در آپلود فایل ${file.name}`);
        }
      }
    } catch (error) {
      console.error('Error uploading files:', error);
      toastService.error('خطا در آپلود فایل‌ها');
    } finally {
      setUploading(false);
    }
  };

  // Remove uploaded document
  const removeDocument = (index) => {
    const newDocs = formData.documents.filter((_, i) => i !== index);
    handleInputChange('documents', newDocs);
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate required fields for appeal requests
    if ((formData.type === 'reportee_review' || formData.type === 'reporter_review') && !formData.requestDate) {
      toastService.error('لطفاً تاریخ درخواست تجدیدنظر را وارد کنید');
      return;
    }

    try {
      setSubmitting(true);

      // Submit appeal request data
      const appealData = {
        revision_status: formData.type,
        revision_date: formData.requestDate,
      };

      const response = await DossierService.updateAppealDossier(uuid, appealData);

      if (response?.data?.status === 'OK') {
        await DossierService.submitDossier(uuid, { description: '---' });
        toastService.success('درخواست تجدیدنظر با موفقیت ثبت شد');
        navigate('/app/inquiry');
      }
    } catch (error) {
      console.error('Error submitting appeal request:', error);
      toastService.error('خطا در ثبت درخواست تجدیدنظر');
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return <Loader />;
  }

  if (!dossier) {
    return (
      <Fragment>
        <Pageheader title="خطا" />
        <Row>
          <Col lg={12}>
            <Card>
              <Card.Body className="text-center">
                <h4>پرونده یافت نشد</h4>
                <Button variant="primary" onClick={() => navigate('/app/inquiry')}>
                  بازگشت به لیست پرونده‌ها
                </Button>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Fragment>
    );
  }

  return (
    <Fragment>
      <Pageheader title={`درخواست تجدیدنظر - پرونده ${dossier.dossier_code || ''}`} />

      {/* Action Buttons */}
      <Row className="mb-3">
        <Col lg={12}>
          <div className="d-flex justify-content-between align-items-center">
            <Button variant="outline-secondary" onClick={() => navigate('/app/inquiry')}>
              <i className="fe fe-arrow-right me-2"></i>
              بازگشت به لیست پرونده‌ها
            </Button>

            <Button variant="outline-info" onClick={() => navigate(`/app/dossier/${uuid}/view`)}>
              <i className="fe fe-eye me-2"></i>
              مشاهده کامل پرونده
            </Button>
          </div>
        </Col>
      </Row>

      <Row>
        {/* Dossier Summary */}
        <Col lg={4}>
          <Card className="mb-4">
            <Card.Header>
              <Card.Title className="mb-0">
                <i className="fe fe-info me-2"></i>
                خلاصه پرونده
              </Card.Title>
            </Card.Header>
            <Card.Body>
              <div className="mb-3">
                <strong>شماره پرونده:</strong> {dossier.dossier_code || '---'}
              </div>
              <div className="mb-3">
                <strong>موسسه:</strong> {dossier.institute.title || 'نامشخص'}
              </div>
              <div className="mb-3">
                <strong>گزارش‌دهنده:</strong> {dossier.reporter?.full_name || 'نامشخص'}
              </div>
              <div className="mb-3">
                <strong>گزارش‌شونده:</strong> {dossier.reportee?.full_name || 'نامشخص'}
              </div>
              <div className="mb-3">
                <strong>تاریخ ایجاد:</strong> {formatJalaliDate(dossier.created_at)}
              </div>

              {/* Show institute opinion summary if available */}
              {dossier.institute_review && (
                <div className="mt-4">
                  <Alert variant="info" className="py-2">
                    <i className="fe fe-file-text me-2"></i>
                    <strong>نظریه کارگروه موسسه:</strong>
                    <br />
                    <small>تاریخ اعلام: {formatJalaliDate(dossier.institute_review.notificationDate)}</small>
                  </Alert>
                </div>
              )}
            </Card.Body>
          </Card>
        </Col>

        {/* Appeal Request Form */}
        <Col lg={8}>
          <Card>
            <Card.Header>
              <Card.Title className="mb-0">
                <i className="fe fe-refresh-cw me-2"></i>
                درخواست تجدیدنظر
              </Card.Title>
            </Card.Header>
            <Card.Body>
              <Alert variant="warning">
                <i className="fe fe-info me-2"></i>
                <strong>بخش 3-2: درخواست تجدیدنظر</strong>
                <br />
                انتخاب گزینه‌های مربوطه پس از صدور نظریه کارگروه موسسه و از تاریخ اطلاع به ذینفعان به مدت دو هفته فعال
                بوده و امکان ثبت درخواست را دارد.
              </Alert>

              <Form onSubmit={handleSubmit}>
                {/* Appeal Type Selection */}
                <Row className="mb-4">
                  <Col md={12}>
                    <Form.Group>
                      <Form.Label>
                        <strong>نوع درخواست تجدیدنظر:</strong>
                        <span className="text-danger ms-1">*</span>
                      </Form.Label>

                      <div className="mt-2">
                        <Form.Check
                          type="radio"
                          id="reportee_review"
                          name="appealType"
                          label="تجدیدنظر توسط گزارش‌شونده"
                          checked={formData.type === 'reportee_review'}
                          onChange={() => handleAppealTypeChange('reportee_review')}
                          className="mb-2"
                        />

                        <Form.Check
                          type="radio"
                          id="reporter_review"
                          name="appealType"
                          label="تجدیدنظر توسط گزارش‌کننده"
                          checked={formData.type === 'reporter_review'}
                          onChange={() => handleAppealTypeChange('reporter_review')}
                          className="mb-2"
                        />

                        <Form.Check
                          type="radio"
                          id="no_review"
                          name="appealType"
                          label="عدم تجدیدنظر"
                          checked={formData.type === 'no_review'}
                          onChange={() => handleAppealTypeChange('no_review')}
                          className="mb-2"
                        />
                      </div>
                    </Form.Group>
                  </Col>
                </Row>

                {/* Appeal Details (shown only if appeal is requested) */}
                {(formData.type === 'reportee_review' || formData.type === 'reporter_review') && (
                  <>
                    <Alert variant="info">
                      <i className="fe fe-info me-2"></i>
                      درخواست تجدیدنظر توسط {formData.type === 'reportee_review' ? 'گزارش‌شونده' : 'گزارش‌کننده'} ثبت
                      شده است. لطفاً اطلاعات مربوطه را تکمیل کنید.
                    </Alert>

                    {/* Request Date */}
                    <Row className="mb-3">
                      <Col md={6}>
                        <Form.Group>
                          <Form.Label>
                            <strong>تاریخ درخواست تجدیدنظر:</strong>
                            <span className="text-danger ms-1">*</span>
                          </Form.Label>
                          <DatePicker
                            value={formData.requestDate ? new Date(formData.requestDate) : null}
                            calendar={persian}
                            locale={persian_fa}
                            calendarPosition="bottom-right"
                            containerClassName={'form-control'}
                            style={{
                              background: 'transparent',
                              width: '100%',
                              boxShadow: 'none!important',
                              outline: 'none',
                              border: 'none',
                            }}
                            onChange={(date) => {
                              const formattedDate = date ? formatDate(new Date(date)) : '';
                              handleInputChange('requestDate', formattedDate);
                            }}
                            placeholder="تاریخ درخواست تجدیدنظر را انتخاب کنید"
                          />
                          <Form.Text className="text-muted">تاریخ ثبت درخواست تجدیدنظر توسط ذینفع</Form.Text>
                        </Form.Group>
                      </Col>
                    </Row>

                    {/* Document Upload */}
                    <Row className="mb-3">
                      <Col md={12}>
                        <Form.Group>
                          <Form.Label>
                            <strong>بارگذاری مستندات درخواست تجدیدنظر:</strong>
                          </Form.Label>
                          <Form.Control
                            type="file"
                            multiple
                            accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                            onChange={handleFileUpload}
                            disabled={uploading}
                          />
                          <Form.Text className="text-muted">
                            فرمت‌های مجاز: PDF, DOC, DOCX, JPG, PNG - امکان انتخاب چندین فایل
                          </Form.Text>
                        </Form.Group>
                      </Col>
                    </Row>

                    {/* Uploaded Documents List */}
                    {formData.documents.length > 0 && (
                      <Row className="mb-3">
                        <Col md={12}>
                          <Form.Label>
                            <strong>فایل‌های آپلود شده:</strong>
                          </Form.Label>
                          <div className="border rounded p-3">
                            {formData.documents.map((doc, index) => (
                              <div key={index} className="d-flex justify-content-between align-items-center mb-2">
                                <div className="d-flex align-items-center">
                                  <i className="fe fe-file me-2"></i>
                                  <span>{doc.name || `فایل ${index + 1}`}</span>
                                </div>
                                <Button variant="outline-danger" size="sm" onClick={() => removeDocument(index)}>
                                  <i className="fe fe-trash-2"></i>
                                </Button>
                              </div>
                            ))}
                          </div>
                        </Col>
                      </Row>
                    )}
                  </>
                )}

                {/* No Appeal Message */}
                {formData.type === 'no_review' && (
                  <Alert variant="success">
                    <i className="fe fe-check me-2"></i>
                    <strong>عدم تجدیدنظر</strong>
                    <br />
                    هیچ درخواست تجدیدنظری از سوی ذینفعان ثبت نشده است. پرونده به مرحله نظریه نهایی منتقل خواهد شد.
                  </Alert>
                )}

                {/* Submit Button */}
                <div className="d-flex justify-content-end mt-4">
                  <Button type="submit" variant="success" disabled={submitting}>
                    {submitting ? (
                      <>
                        <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        در حال ثبت...
                      </>
                    ) : (
                      <>
                        <i className="fe fe-check me-2"></i>
                        ثبت درخواست و انتقال به مرحله بعد
                      </>
                    )}
                  </Button>
                </div>

                {/* Process Information */}
                <Alert variant="info" className="mt-3">
                  <i className="fe fe-info me-2"></i>
                  <strong>نکته مهم:</strong>
                  {formData.type !== 'no_review' ? (
                    <>
                      پرونده‌هایی که درخواست تجدیدنظر داشته‌اند باید توسط کارگروه موسسه به کارگروه وزارتی ارسال شود و در
                      کارتابل کارگروه وزارتی قابل دریافت و مشاهده باشند.
                    </>
                  ) : (
                    <>در صورت عدم درخواست تجدیدنظر، پرونده مستقیماً به مرحله نظریه نهایی کارگروه موسسه منتقل می‌شود.</>
                  )}
                </Alert>
              </Form>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Fragment>
  );
};

export default AppealRequest;
