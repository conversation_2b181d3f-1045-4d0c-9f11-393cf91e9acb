import { create } from 'zustand';

const oneYearInMillis = 12 * 30 * 24 * 60 * 60 * 1000;

const initialState = {
  filter: {
    institute_id: null,
    start_date: null,
    end_date: null,
    reloadData: false,
  },
  loading: false,
  chartTypes: {},
};

// Create Zustand store
const useChartFilterStore = create((set, get) => ({
  ...initialState,
  setFilter: (newFilter) => set((state) => ({ filter: { ...state.filter, ...newFilter } })),
  setLoading: (newData) => set((state) => ({ loading: newData })),
  setChartTypes: (chartTypes) => set((state) => ({ chartTypes })),
  removeFilter: (rest) => set((state) => ({ filter: { ...rest } })),
  clearFilter: () => {
    set({ filter: initialState.filter });
  },
}));

export default useChartFilterStore;
