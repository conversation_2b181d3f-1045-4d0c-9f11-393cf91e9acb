import React, { Suspense, useContext } from 'react';
import { Navigate, Route, Routes } from 'react-router-dom';
import './index.scss';
import Loader from 'layout/layoutcomponent/loaders';
import RouteGuard from './route-guard.jsx';
import AuthContext from 'context/auth-context.jsx';
import Profile from 'pages/profile/index.jsx';
import ViolationDetails from './pages/violation-details/index.jsx';

// Component to protect ministerial routes
const MinisterialRouteGuard = ({ children }) => {
  const { profile } = useContext(AuthContext);

  if (!profile?.is_ministerial) {
    return <Navigate replace to="/app/dashboard" />;
  }

  return children;
};

const Layout = React.lazy(() => import('./layout/Layout.jsx'));
const Dashboard = React.lazy(() => import('pages/dashboard'));
const Inquiry = React.lazy(() => import('./pages/inquiry'));
const Users = React.lazy(() => import('./pages/users'));
const Institute = React.lazy(() => import('./pages/institute'));
const ChartReports = React.lazy(() => import('./pages/chart-reports'));
const SubmitDocument = React.lazy(() => import('./pages/submit-document'));
const DossierView = React.lazy(() => import('./pages/dossier-view'));
const ExpertReview = React.lazy(() => import('./pages/expert-review'));
const InitialOpinion = React.lazy(() => import('./pages/initial-opinion'));
const AppealRequest = React.lazy(() => import('./pages/appeal-request'));
const MinistryOpinion = React.lazy(() => import('./pages/ministry-opinion'));
const FinalOpinion = React.lazy(() => import('./pages/final-opinion'));
const Referred = React.lazy(() => import('./pages/referred'));
const RulingIssued = React.lazy(() => import('./pages/ruling-issued'));

//pages
const SignIn = React.lazy(() => import('pages/signin'));
// const ForgotPassword = React.lazy(() => import('./components/pages/authentication/forgotpassword/forgotpassword.jsx'));

const Error404 = React.lazy(() => import('pages/404'));
const Error500 = React.lazy(() => import('pages/500'));

function App() {
  const { isLoading, profile } = useContext(AuthContext);

  return isLoading ? (
    <Loader />
  ) : (
    <Suspense fallback={<Loader />}>
      <Routes>
        <Route path="/" element={<Navigate replace to={'/app/dashboard'} />} />
        <Route path="/app" element={<RouteGuard checkWelcome={false} Component={Layout} />}>
          <Route index element={<Navigate replace to="/app/dashboard" />} />
          <Route path={'/app/dashboard'} element={<Dashboard />} />
          <Route path={'/app/profile'} element={<Profile />} />
          <Route path={'/app/submit-document'} element={<SubmitDocument />} />
          <Route path={'/app/submit-document/:uuid'} element={<SubmitDocument />} />
          <Route path={'/app/inquiry'} element={<Inquiry />} />
          <Route path={'/app/dossier/:uuid/view'} element={<DossierView />} />
          <Route path={'/app/dossier/:uuid/expert-review'} element={<ExpertReview />} />
          <Route path={'/app/dossier/:uuid/initial-opinion'} element={<InitialOpinion />} />
          <Route path={'/app/dossier/:uuid/appeal-request'} element={<AppealRequest />} />
          <Route path={'/app/dossier/:uuid/final-opinion'} element={<FinalOpinion />} />
          <Route path={'/app/dossier/:uuid/violation-details'} element={<ViolationDetails />} />
          <Route path={'/app/dossier/:uuid/referred'} element={<Referred />} />
          <Route path={'/app/dossier/:uuid/ruling-issued'} element={<RulingIssued />} />
          <Route
            path={'/app/dossier/:uuid/ministry-opinion'}
            element={
              <MinisterialRouteGuard>
                <MinistryOpinion />
              </MinisterialRouteGuard>
            }
          />
          <Route
            path={'/app/institute'}
            element={
              <MinisterialRouteGuard>
                <Institute />
              </MinisterialRouteGuard>
            }
          />
          <Route
            path={'/app/users'}
            element={
              <MinisterialRouteGuard>
                <Users />
              </MinisterialRouteGuard>
            }
          />
          <Route path={'/app/chart-reports'} element={<ChartReports />} />
        </Route>

        <Route path="/login" element={<SignIn />} />
        {/*<Route path="/forgot-password" element={<ForgotPassword />} />*/}

        <Route path="/500" element={<Error500 />} />
        <Route path="*" element={<Error404 />} />
      </Routes>
    </Suspense>
  );
}

export default App;
