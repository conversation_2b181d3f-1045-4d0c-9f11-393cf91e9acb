import React from 'react';
import ReactD<PERSON> from 'react-dom/client';
import { <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom';
import './index.scss';
import { AuthContextProvider } from './context/auth-context.jsx';
import App from './App.jsx';
import { ToastContainer } from 'react-toastify';

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <AuthContextProvider>
      <BrowserRouter>
        <App />
        <ToastContainer rtl={true} position="top-left" autoClose={5000} hideProgressBar={false} />
      </BrowserRouter>
    </AuthContextProvider>
  </React.StrictMode>
);
