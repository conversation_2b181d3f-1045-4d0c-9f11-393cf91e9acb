import React, { useState, Fragment, useEffect, useContext } from 'react';
import { Card, Col, Row, Button, Badge } from 'react-bootstrap';
import useGeneralStore from 'store/generalStore.js';
import useSearchStore from 'store/searchStore.js';
import Filters from 'pages/chart-reports/components/filters.jsx';
import useChartFilterStore from 'store/chartFilterStore.js';
import TabToTop from 'layout/layoutcomponent/tabtotop.jsx';
import Select from 'react-select';
import StatisticsService from 'service/api/statisticsService.js';
import AuthContext from 'context/auth-context.jsx';

// Generic chart component
import CaseFrequencyByYearChart from 'pages/chart-reports/components/case-frequency-by-year-chart.jsx';

// Generic chart component that works with all chart types
const GenericChart = ({ chartType }) => {
  return <CaseFrequencyByYearChart chartType={chartType} />;
};

const ChartReports = () => {
  const { setData } = useGeneralStore();
  const { clearFilter } = useSearchStore();
  const { chartTypes, setChartTypes, filter } = useChartFilterStore();
  const { profile } = useContext(AuthContext);

  const [selectedCharts, setSelectedCharts] = useState([]);
  const [loading, setLoading] = useState(false);

  // Load chart types from API
  useEffect(() => {
    const loadChartTypes = async () => {
      try {
        setLoading(true);
        const response = await StatisticsService.getChartTypes();
        if (response?.data?.data?.charts) {
          setChartTypes(response.data.data.charts);
        }
      } catch (error) {
        console.error('Error loading chart types:', error);
      } finally {
        setLoading(false);
      }
    };

    loadChartTypes();
  }, [setChartTypes]);

  useEffect(() => {
    setData({ pageTitle: 'گزارش‌های آماری' });
    clearFilter();
  }, [setData, clearFilter]);

  // Get all chart options for the select dropdown
  const getChartOptions = () => {
    return Object.entries(chartTypes).map(([key, title]) => ({
      value: key,
      label: title,
    }));
  };

  // Handle chart selection
  const handleChartSelection = (selectedOptions) => {
    const chartIds = selectedOptions ? selectedOptions.map((option) => option.value) : [];
    setSelectedCharts(chartIds);
  };

  // Select all charts
  const handleSelectAll = () => {
    const allChartIds = Object.keys(chartTypes);
    setSelectedCharts(allChartIds);
  };

  // Clear all selected charts
  const handleClearAll = () => {
    setSelectedCharts([]);
  };

  // Get filtered charts to display
  const getChartsToDisplay = () => {
    if (selectedCharts.length === 0) {
      return []; // Show no charts if none selected
    }
    return selectedCharts.map((chartId) => ({
      id: chartId,
      title: chartTypes[chartId] || chartId,
      icon: 'fe fe-bar-chart',
      size: 'full',
    }));
  };

  // Render individual chart
  const renderChart = (chartConfig) => {
    const colSize = chartConfig.size === 'half' ? 6 : 12;

    return (
      <Col sm={colSize} key={chartConfig.id}>
        <Card className="overflow-hidden">
          <Card.Header className="border-bottom-0 d-flex">
            <h3 className="card-title mb-2">
              <i className={`${chartConfig.icon} tx-primary mg-r-5`} />
              {chartConfig.title}
            </h3>
          </Card.Header>
          <Card.Body>
            <GenericChart chartType={chartConfig.id} />
          </Card.Body>
        </Card>
      </Col>
    );
  };

  return (
    <Fragment>
      <TabToTop />
      <Row className="row-sm mg-t-15">
        <Col xl={12} lg={12} md={12}>
          {/* Filters */}
          <Row className="row-sm mb-3">
            <Col xl={12}>
              <Filters />
            </Col>
          </Row>

          {/* Chart Selection Interface */}
          <Row className="row-sm mb-3">
            <Col xl={12}>
              <Card>
                <Card.Header className="border-bottom-0 d-flex justify-content-between align-items-center">
                  <h3 className="card-title mb-0">
                    <i className="fe fe-filter tx-primary mg-r-5" />
                    انتخاب نمودارها
                  </h3>
                  <Badge bg="primary" className="tx-12">
                    {selectedCharts.length} نمودار انتخاب شده
                  </Badge>
                </Card.Header>
                <Card.Body className="p-3">
                  <Row className="align-items-center">
                    <Col md={8}>
                      <Select
                        isMulti
                        options={getChartOptions()}
                        placeholder="نمودارهای مورد نظر خود را انتخاب کنید..."
                        classNamePrefix="Select2"
                        value={getChartOptions().filter((option) => selectedCharts.includes(option.value))}
                        onChange={handleChartSelection}
                        noOptionsMessage={() => 'نمودار موجود نیست'}
                        isSearchable
                        isLoading={loading}
                      />
                    </Col>
                    <Col md={4} className="d-flex gap-2">
                      <Button variant="outline-primary" size="sm" onClick={handleSelectAll} className="me-2">
                        انتخاب همه
                      </Button>
                      <Button variant="outline-secondary" size="sm" onClick={handleClearAll}>
                        پاک کردن
                      </Button>
                    </Col>
                  </Row>

                  {selectedCharts.length === 0 && (
                    <div className="alert alert-info mt-3 mb-0" role="alert">
                      <i className="fe fe-info mg-r-5"></i>
                      لطفاً نمودارهای مورد نظر خود را از لیست بالا انتخاب کنید.
                    </div>
                  )}
                </Card.Body>
              </Card>
            </Col>
          </Row>
          {/* Dynamic Chart Rendering */}
          <Row className="row-sm">
            {/* Check if institute is selected for ministerial users */}
            {profile?.is_ministerial && !filter.institute_id ? (
              <Col sm={12}>
                <Card className="text-center">
                  <Card.Body className="p-5">
                    <div className="empty-state">
                      <i className="fe fe-building tx-100 tx-muted mb-3"></i>
                      <h4 className="tx-muted">کارگروه انتخاب نشده است</h4>
                      <p className="tx-muted mb-4">لطفاً ابتدا کارگروه مورد نظر خود را از بخش فیلترها انتخاب کنید.</p>
                    </div>
                  </Card.Body>
                </Card>
              </Col>
            ) : getChartsToDisplay().length > 0 ? (
              <>
                {/* Category Header */}
                <h3
                  className="tx-20 h-6 tx-bold mg-l-10 mb-3 w-100"
                  style={{ padding: '2px 6px', borderRight: '8px solid #38CAB3' }}
                >
                  نمودارهای انتخاب شده
                </h3>

                {/* Render Selected Charts */}
                {getChartsToDisplay().map((chartConfig) => renderChart(chartConfig))}
              </>
            ) : (
              <Col sm={12}>
                <Card className="text-center">
                  <Card.Body className="p-5">
                    <div className="empty-state">
                      <i className="fe fe-bar-chart tx-100 tx-muted mb-3"></i>
                      <h4 className="tx-muted">هیچ نموداری انتخاب نشده است</h4>
                      <p className="tx-muted mb-4">
                        لطفاً از بخش انتخاب نمودارها، نمودارهای مورد نظر خود را انتخاب کنید.
                      </p>
                      <Button variant="primary" onClick={handleSelectAll}>
                        انتخاب همه نمودارها
                      </Button>
                    </div>
                  </Card.Body>
                </Card>
              </Col>
            )}
          </Row>
        </Col>
      </Row>
    </Fragment>
  );
};

ChartReports.propTypes = {};

export default ChartReports;
