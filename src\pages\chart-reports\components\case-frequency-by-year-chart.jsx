import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import loader from 'assets/img/loader.svg';
import useChartFilterStore from 'store/chartFilterStore.js';
import { formatShortNumber } from 'utils/helper.js';
import moment from 'moment-jalaali';
import StatisticsService from 'service/api/statisticsService.js';

const CaseFrequencyByYearChart = ({ chartType }) => {
  const [loading, setLoading] = useState(false);
  const [chartOptions, setChartOptions] = useState(null);
  const [hasValue, setHasValue] = useState(false);
  const { filter } = useChartFilterStore();

  const toPersianNumber = (number) => {
    const persianDigits = '۰۱۲۳۴۵۶۷۸۹';
    return number.toString().replace(/\d/g, (d) => persianDigits[d]);
  };

  const reloadData = async () => {
    try {
      setLoading(true);

      // Prepare request data
      const requestData = {
        institute_id: filter.institute_id,
        start_date: filter.start_date,
        end_date: filter.end_date,
        chart_type: chartType || 'dossiers_by_year',
      };

      const response = await StatisticsService.getChart(requestData);
      const data = response?.data?.data?.data || [];

      if (data.length > 0) setHasValue(true);
      else setHasValue(false);

      // Process data based on chart type
      let categories = [];
      let seriesData = [];
      let chartType_display = 'column';

      if (Array.isArray(data)) {
        // For array data (like year-based charts)
        categories = data.map((item, index) => item.name || item.label || `مورد ${index + 1}`);
        seriesData = data.map((item) => item.value || item.count || 0);
      } else if (typeof data === 'object') {
        // For object data (like pie charts)
        categories = Object.keys(data);
        seriesData = Object.values(data);
        chartType_display = 'pie';
      }

      setChartOptions({
        chart: {
          type: chartType_display,
          height: '300px',
          backgroundColor: '#fff',
          style: {
            borderRadius: '8px',
          },
        },
        title: {
          text: '',
        },
        legend: {
          enabled: chartType_display === 'pie',
          layout: 'vertical',
          align: 'right',
          verticalAlign: 'middle',
          itemStyle: {
            fontFamily: 'iranyekan',
            fontSize: '12px',
          },
        },
        credits: {
          enabled: false,
        },
        xAxis:
          chartType_display === 'column'
            ? {
                categories,
                labels: {
                  formatter: function () {
                    return toPersianNumber(this.value.toString());
                  },
                },
              }
            : undefined,
        yAxis:
          chartType_display === 'column'
            ? {
                min: 0,
                title: {
                  text: null,
                },
                labels: {
                  formatter: function () {
                    return toPersianNumber(formatShortNumber(this.value));
                  },
                },
              }
            : undefined,
        tooltip: {
          enabled: true,
          useHTML: true,
          formatter: function () {
            if (chartType_display === 'pie') {
              return `
                <div style="font-family:'iranyekan',serif;font-size:11px;direction:rtl">
                  <div style="display:flex;align-items:center;margin-bottom:5px;">
                    <div style="width:10px;height:10px;border-radius:50%;background-color:${this.point.color};margin-left:3px;"></div>
                    <span>${this.point.name}:</span>
                    <span style="color:#333;margin-right:5px;">${toPersianNumber(formatShortNumber(this.y))}</span>
                  </div>
                  <div style="display:flex;align-items:center;">
                    <span>درصد:</span>
                    <span style="color:#333;margin-right:5px;">${toPersianNumber(this.percentage.toFixed(1))}%</span>
                  </div>
                </div>`;
            } else {
              return `
                <div style="font-family:'iranyekan',serif;font-size:11px;direction:rtl">
                  <div style="display:flex;align-items:center;margin-bottom:5px;">
                    <div style="width:10px;height:10px;border-radius:50%;background-color:${this.point.color};margin-left:3px;"></div>
                    <span>${this.x}:</span>
                    <span style="color:#333;margin-right:5px;">${toPersianNumber(formatShortNumber(this.y))}</span>
                  </div>
                </div>`;
            }
          },
        },
        plotOptions: {
          column: {
            borderWidth: 0,
          },
          pie: {
            allowPointSelect: true,
            cursor: 'pointer',
            dataLabels: {
              enabled: true,
              format: '<b>{point.name}</b>: {point.percentage:.1f} %',
              style: {
                fontFamily: 'iranyekan',
                fontSize: '11px',
              },
            },
          },
        },
        series:
          chartType_display === 'pie'
            ? [
                {
                  name: 'مقادیر',
                  colorByPoint: true,
                  data: categories.map((cat, index) => ({
                    name: cat,
                    y: seriesData[index],
                  })),
                },
              ]
            : [
                {
                  name: 'مقادیر',
                  data: seriesData,
                  color: '#38CAB3',
                },
              ],
      });
    } catch (e) {
      console.log(e);
      setHasValue(false);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (chartType && filter.institute_id) {
      reloadData();
    }
  }, [chartType, filter.institute_id, filter.start_date, filter.end_date, filter.reloadData]);

  return loading ? (
    <div className="col-wrapper" style={{ height: '300px' }}>
      <img src={loader} className="loader-img" alt="Loader" />
    </div>
  ) : hasValue ? (
    <div className="w-full">
      <HighchartsReact highcharts={Highcharts} options={chartOptions} />
    </div>
  ) : (
    <div
      className="d-flex flex-1"
      style={{ minHeight: '230px', textAlign: 'center', justifyContent: 'center', alignItems: 'center' }}
    >
      داده‌ای برای نمایش وجود ندارد
    </div>
  );
};

CaseFrequencyByYearChart.propTypes = {
  chartType: PropTypes.string.isRequired,
};

export default CaseFrequencyByYearChart;
