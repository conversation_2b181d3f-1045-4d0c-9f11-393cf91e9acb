import useFetch from '../index';

class DossierService {
  getList() {
    return useFetch.get('/api/v1/dossier/list/');
  }

  getDossier(uuid) {
    if (!uuid) return false;
    return useFetch.get(`/api/v1/dossier/retrieve/${uuid}/`);
  }

  addNewDossier(data) {
    return useFetch.post('/api/v1/dossier/new/', data);
  }

  updateDossier(uuid, data) {
    if (!uuid) return false;
    return useFetch.put(`/api/v1/dossier/new/${uuid}/`, data);
  }

  updateExpertDossier(uuid, data) {
    if (!uuid) return false;
    return useFetch.put(`/api/v1/dossier/assessment/expert/${uuid}/`, data);
  }

  updateRulingIssuedDossier(uuid, data) {
    if (!uuid) return false;
    return useFetch.put(`/api/v1/dossier/assessment/ruling-issued/${uuid}/`, data);
  }

  updateAppealDossier(uuid, data) {
    if (!uuid) return false;
    return useFetch.put(`/api/v1/dossier/assessment/appeal-request/${uuid}/`, data);
  }

  updateMinistryOpinionDossier(uuid, data) {
    if (!uuid) return false;
    return useFetch.put(`/api/management/v1/dossier/assessment/ministry-opinion/${uuid}/`, data);
  }

  updateReferredDossier(uuid, data) {
    if (!uuid) return false;
    return useFetch.put(`/api/v1/dossier/assessment/referred/${uuid}/`, data);
  }

  updateInitialOpponionDossier(uuid, data) {
    if (!uuid) return false;
    return useFetch.put(`/api/v1/dossier/assessment/initial-opinion/${uuid}/`, data);
  }

  updateFinalOpponionDossier(uuid, data) {
    if (!uuid) return false;
    return useFetch.put(`/api/v1/dossier/assessment/final-opinion/${uuid}/`, data);
  }

  updateViolationDetailsDossier(uuid, data) {
    if (!uuid) return false;
    return useFetch.put(`/api/v1/dossier/assessment/violation-details/${uuid}/`, data);
  }

  submitDossier(uuid, data) {
    if (!uuid) return false;
    return useFetch.post(`/api/v1/dossier/submit/${uuid}/`, data);
  }

  deleteDossier(uuid) {
    if (!uuid) return false;
    return useFetch.delete(`/api/v1/dossier/delete/${uuid}/`);
  }

  uploadDocument(formData) {
    return useFetch.post('api/v1/dossier/documents/upload/', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  getDocuments(dossierUuid, documentSource = null, order = 'desc') {
    if (!dossierUuid) return false;
    let url = `/api/v1/dossier/documents/list/?dossier_uuid=${dossierUuid}&order=${order}`;
    if (documentSource) {
      url += `&document_source=${documentSource}`;
    }
    return useFetch.get(url);
  }
}
export default new DossierService();
