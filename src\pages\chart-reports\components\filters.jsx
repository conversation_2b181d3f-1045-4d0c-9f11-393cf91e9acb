import { Card, Form, FormGroup } from 'react-bootstrap';
import DatePicker from 'react-multi-date-picker';
import persian from 'react-date-object/calendars/persian';
import persian_fa from 'react-date-object/locales/persian_fa';
import Select from 'react-select';
import React, { Fragment, useEffect, useState, useContext } from 'react';
import { formatDate } from 'utils/helper.js';
import useChartFilterStore from 'store/chartFilterStore.js';
import StatisticsService from 'service/api/statisticsService.js';
import AuthContext from 'context/auth-context.jsx';
import InstituteService from 'service/api/instituteService.js';

const Filters = ({ validationErrors }) => {
  const { filter, setFilter, removeFilter } = useChartFilterStore();
  const { profile } = useContext(AuthContext);
  const [institutes, setInstitutes] = useState([]);
  const [loadingInstitutes, setLoadingInstitutes] = useState(false);

  // Load institutes list for ministerial users
  useEffect(() => {
    const loadInstitutes = async () => {
      if (profile?.is_ministerial) {
        try {
          setLoadingInstitutes(true);
          const response = await InstituteService.getList({ page_size: 1000 });
          if (response?.data?.data?.institutes) {
            setInstitutes(
              response.data.data.institutes.map((institute) => ({
                value: institute.uuid,
                label: institute.title,
              }))
            );
          }
        } catch (error) {
          console.error('Error loading institutes:', error);
        } finally {
          setLoadingInstitutes(false);
        }
      }
    };

    loadInstitutes();
  }, [profile?.is_ministerial]);

  useEffect(() => {
    // Set institute_id from user profile if available (for non-ministerial users)
    if (profile?.institute?.uuid && !filter.institute_id && !profile?.is_ministerial) {
      setFilter({ institute_id: profile.institute.uuid });
    }
  }, [profile, filter.institute_id, setFilter]);

  return (
    <Fragment>
      <Card>
        <Card.Header className="border-bottom-0 d-flex">
          <h3 className="card-title mb-2">
            <i className="fe fe-filter tx-primary mg-r-5" />
            فیلترهای نمودار
          </h3>
        </Card.Header>
        <Card.Body className="p-3">
          {/* Institute selector for ministerial users */}
          {profile?.is_ministerial && (
            <FormGroup className="form-group">
              <Form.Label htmlFor="institute">انتخاب کارگروه</Form.Label>
              <Select
                options={institutes}
                placeholder="کارگروه مورد نظر را انتخاب کنید..."
                classNamePrefix="Select2"
                value={institutes.find((institute) => institute.value === filter.institute_id) || null}
                onChange={(selectedOption) => {
                  setFilter({ institute_id: selectedOption ? selectedOption.value : null });
                }}
                noOptionsMessage={() => 'کارگروهی موجود نیست'}
                isSearchable
                isLoading={loadingInstitutes}
                isClearable
              />
            </FormGroup>
          )}

          <FormGroup className="form-group pos-relative">
            <Form.Label htmlFor="date_range">بازه زمانی (انتخاب بازه)</Form.Label>
            <DatePicker
              value={[
                filter.start_date ? new Date(filter.start_date) : null,
                filter.end_date ? new Date(filter.end_date) : null,
              ]}
              calendar={persian}
              locale={persian_fa}
              calendarPosition="bottom-right"
              containerClassName={'form-control'}
              range
              style={{
                background: 'transparent',
                width: '100%',
                boxShadow: 'none!important',
                outline: 'none',
                border: 'none',
              }}
              onChange={(e) => {
                const [start, end] = e;
                setFilter({
                  start_date: start ? formatDate(new Date(start)) : null,
                  end_date: end ? formatDate(new Date(end)) : null,
                });
              }}
            />
            <div
              className={'fe fe-x-circle'}
              style={{ position: 'absolute', cursor: 'pointer', top: '38px', left: '10px' }}
              onClick={() => {
                setFilter({
                  start_date: null,
                  end_date: null,
                });
              }}
            ></div>
          </FormGroup>
        </Card.Body>
      </Card>
    </Fragment>
  );
};

export default Filters;
