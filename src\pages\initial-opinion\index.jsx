import { Fragment, useEffect, useState } from 'react';
import { Card, Col, Row, Button, Form, Alert } from 'react-bootstrap';
import { useParams, useNavigate } from 'react-router-dom';
import Pageheader from 'layout/layoutcomponent/pageheader.jsx';
import useGeneralStore from 'store/generalStore.js';
import DossierService from 'service/api/dossierService.js';
import toastService from 'utils/toastService.js';
import Loader from 'layout/layoutcomponent/loaders';
import { formatJalaliDate } from 'utils/helper.js';

import DatePicker from 'react-multi-date-picker';
import persian from 'react-date-object/calendars/persian';
import persian_fa from 'react-date-object/locales/persian_fa';
import { formatDate } from '../../utils/helper';

const InitialOpinion = () => {
  const { uuid } = useParams();
  const navigate = useNavigate();
  const { setData } = useGeneralStore();

  const [dossier, setDossier] = useState(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    meetingDate: '',
    meetingMembers: '',
    statements: '',
    opinion: '',
    opinionDocument: null,
    notificationDate: '',
  });

  // Fetch dossier data
  useEffect(() => {
    const fetchDossierData = async () => {
      if (!uuid) {
        toastService.error('شناسه پرونده معتبر نیست');
        navigate('/app/inquiry');
        return;
      }

      try {
        setLoading(true);
        const response = await DossierService.getDossier(uuid);
        const dossierData = response?.data?.data;

        if (dossierData) {
          setDossier(dossierData);
          setData({ pageTitle: `صدور نظریه بدوی کارگروه موسسه - پرونده ${dossierData.dossier_code || ''}` });

          // Check if dossier is in correct status
          if (dossierData.dossier_status !== 'initial_opinion') {
            toastService.error('این پرونده در مرحله صدور نظریه بدوی نیست');
            navigate('/app/inquiry');
            return;
          }

          // Load existing data if available
          if (dossierData.initial_opinion) {
            setFormData({
              meetingDate: dossierData.initial_opinion.meetingDate || '',
              meetingMembers: dossierData.initial_opinion.meetingMembers || '',
              statements: dossierData.initial_opinion.statements || '',
              opinion: dossierData.initial_opinion.opinion || '',
              opinionDocument: dossierData.initial_opinion.opinionDocument || null,
              notificationDate: dossierData.initial_opinion.notificationDate || '',
            });
          }
        } else {
          throw new Error('Dossier data not found');
        }
      } catch (error) {
        console.error('Error fetching dossier:', error);
        toastService.error('خطا در دریافت اطلاعات پرونده');
        navigate('/app/inquiry');
      } finally {
        setLoading(false);
      }
    };

    fetchDossierData();
  }, [uuid, setData, navigate]);

  // Handle form input changes
  const handleInputChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle file upload
  const handleFileUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    try {
      setSubmitting(true);
      const formDataUpload = new FormData();
      formDataUpload.append('document', file);
      formDataUpload.append('document_source', 'initial_opinion');
      formDataUpload.append('dossier', uuid);

      const response = await DossierService.uploadDocument(formDataUpload);

      handleInputChange('opinionDocument', response.data);
      toastService.success('فایل با موفقیت آپلود شد');
    } catch (error) {
      console.error('Error uploading file:', error);
      toastService.error('خطا در آپلود فایل');
    } finally {
      setSubmitting(false);
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (
      !formData.meetingDate ||
      !formData.meetingMembers ||
      !formData.statements ||
      !formData.opinion ||
      !formData.notificationDate
    ) {
      toastService.error('لطفاً تمام فیلدهای الزامی را تکمیل کنید');
      return;
    }

    try {
      setSubmitting(true);

      // Submit initial opinion data
      const opinionData = {
        initial_opinion: {
          session_date: formData.meetingDate,
          opinion_announcement_date: formData.notificationDate,
          opinion: formData.opinion,
          session_members: formData.meetingMembers,
          statements: formData.statements,
        },
      };

      const response = await DossierService.updateInitialOpponionDossier(uuid, opinionData);

      if (response?.data?.status === 'OK') {
        await DossierService.submitDossier(uuid, { description: '---' });
        toastService.success('نظریه بدوی کارگروه موسسه با موفقیت ثبت شد');
        navigate('/app/inquiry');
      }
    } catch (error) {
      console.error('Error submitting initial opinion:', error);
      toastService.error('خطا در ثبت نظریه بدوی کارگروه موسسه');
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return <Loader />;
  }

  if (!dossier) {
    return (
      <Fragment>
        <Pageheader title="خطا" />
        <Row>
          <Col lg={12}>
            <Card>
              <Card.Body className="text-center">
                <h4>پرونده یافت نشد</h4>
                <Button variant="primary" onClick={() => navigate('/app/inquiry')}>
                  بازگشت به لیست پرونده‌ها
                </Button>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Fragment>
    );
  }

  return (
    <Fragment>
      <Pageheader title={`صدور نظریه بدوی کارگروه موسسه - پرونده ${dossier.dossier_code || ''}`} />

      {/* Action Buttons */}
      <Row className="mb-3">
        <Col lg={12}>
          <div className="d-flex justify-content-between align-items-center">
            <Button variant="outline-secondary" onClick={() => navigate('/app/inquiry')}>
              <i className="fe fe-arrow-right me-2"></i>
              بازگشت به لیست پرونده‌ها
            </Button>

            <Button variant="outline-info" onClick={() => navigate(`/app/dossier/${uuid}/view`)}>
              <i className="fe fe-eye me-2"></i>
              مشاهده کامل پرونده
            </Button>
          </div>
        </Col>
      </Row>

      <Row>
        {/* Dossier Summary */}
        <Col lg={4}>
          <Card className="mb-4">
            <Card.Header>
              <Card.Title className="mb-0">
                <i className="fe fe-info me-2"></i>
                خلاصه پرونده
              </Card.Title>
            </Card.Header>
            <Card.Body>
              <div className="mb-3">
                <strong>شماره پرونده:</strong> {dossier.dossier_code || '---'}
              </div>
              <div className="mb-3">
                <strong>موسسه:</strong> {dossier.institute.title || 'نامشخص'}
              </div>
              <div className="mb-3">
                <strong>گزارش‌دهنده:</strong> {dossier.reporter?.full_name || 'نامشخص'}
              </div>
              <div className="mb-3">
                <strong>گزارش‌شونده:</strong> {dossier.reportee?.full_name || 'نامشخص'}
              </div>
              <div className="mb-3">
                <strong>تاریخ ایجاد:</strong> {formatJalaliDate(dossier.created_at)}
              </div>
            </Card.Body>
          </Card>
        </Col>

        {/* Initial Opinion Form */}
        <Col lg={8}>
          <Card>
            <Card.Header>
              <Card.Title className="mb-0">
                <i className="fe fe-file-text me-2"></i>
                صدور نظریه بدوی کارگروه موسسه
              </Card.Title>
            </Card.Header>
            <Card.Body>
              <Alert variant="info">
                <i className="fe fe-info me-2"></i>
                <strong>صدور نظریه بدوی کارگروه موسسه</strong>
                <br />
                در این مرحله، کارگروه موسسه پس از بررسی مستندات و انجام کارشناسی، نظریه بدوی خود را صادر می‌کند.
              </Alert>

              <Form onSubmit={handleSubmit}>
                {/* Meeting Date & Notification Date */}
                <Row className="mb-3">
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label>
                        <strong>تاریخ جلسه:</strong>
                        <span className="text-danger ms-1">*</span>
                      </Form.Label>
                      <DatePicker
                        value={formData.meetingDate ? new Date(formData.meetingDate) : null}
                        calendar={persian}
                        locale={persian_fa}
                        calendarPosition="bottom-right"
                        containerClassName={'form-control'}
                        style={{
                          background: 'transparent',
                          width: '100%',
                          boxShadow: 'none!important',
                          outline: 'none',
                          border: 'none',
                        }}
                        onChange={(date) => {
                          const formattedDate = date ? formatDate(new Date(date)) : '';
                          handleInputChange('meetingDate', formattedDate);
                        }}
                        placeholder="تاریخ جلسه را انتخاب کنید"
                      />
                      {/* <div
                        className="invalid-feedback"
                        style={{ display: validationErrors.reportReceivedDate ? 'block' : 'none' }}
                      >
                        {validationErrors.reportReceivedDate}
                      </div> */}
                      {/* <Form.Control
                        type="date"
                        value={formData.meetingDate}
                        onChange={(e) => handleInputChange('meetingDate', e.target.value)}
                        required
                      /> */}
                    </Form.Group>
                  </Col>

                  <Col md={6}>
                    <Form.Group>
                      <Form.Label>
                        <strong>تاریخ اعلام نظریه به ذینفعان:</strong>
                        <span className="text-danger ms-1">*</span>
                      </Form.Label>
                      <DatePicker
                        value={formData.notificationDate ? new Date(formData.notificationDate) : null}
                        calendar={persian}
                        locale={persian_fa}
                        calendarPosition="bottom-right"
                        containerClassName={'form-control'}
                        style={{
                          background: 'transparent',
                          width: '100%',
                          boxShadow: 'none!important',
                          outline: 'none',
                          border: 'none',
                        }}
                        onChange={(date) => {
                          const formattedDate = date ? formatDate(new Date(date)) : '';
                          handleInputChange('notificationDate', formattedDate);
                        }}
                        placeholder="تاریخ اعلام نظریه را انتخاب کنید"
                      />
                    </Form.Group>
                  </Col>
                </Row>

                {/* Meeting Members */}
                <Row className="mb-3">
                  <Col md={12}>
                    <Form.Group>
                      <Form.Label>
                        <strong>اعضای جلسه:</strong>
                        <span className="text-danger ms-1">*</span>
                      </Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={4}
                        value={formData.meetingMembers}
                        onChange={(e) => handleInputChange('meetingMembers', e.target.value)}
                        placeholder="نام و سمت اعضای حاضر در جلسه کارگروه را وارد کنید..."
                        required
                      />
                      <Form.Text className="text-muted">
                        لطفاً نام کامل و سمت هر یک از اعضای حاضر در جلسه را در خطوط جداگانه وارد کنید.
                      </Form.Text>
                    </Form.Group>
                  </Col>
                </Row>

                {/* Statements */}
                <Row className="mb-3">
                  <Col md={12}>
                    <Form.Group>
                      <Form.Label>
                        <strong>اظهارات:</strong>
                        <span className="text-danger ms-1">*</span>
                      </Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={6}
                        value={formData.statements}
                        onChange={(e) => handleInputChange('statements', e.target.value)}
                        placeholder="اظهارات و بحث‌های مطرح شده در جلسه کارگروه را وارد کنید..."
                        required
                      />
                      <Form.Text className="text-muted">
                        خلاصه‌ای از بحث‌ها، نظرات و اظهارات مطرح شده در جلسه کارگروه را بنویسید.
                      </Form.Text>
                    </Form.Group>
                  </Col>
                </Row>

                {/* Opinion */}
                <Row className="mb-3">
                  <Col md={12}>
                    <Form.Group>
                      <Form.Label>
                        <strong>نظریه بدوی:</strong>
                        <span className="text-danger ms-1">*</span>
                      </Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={8}
                        value={formData.opinion}
                        onChange={(e) => handleInputChange('opinion', e.target.value)}
                        placeholder="نظریه بدوی کارگروه موسسه را وارد کنید..."
                        required
                      />
                      <Form.Text className="text-muted">
                        نظریه و تصمیم بدوی کارگروه موسسه در خصوص پرونده مورد بررسی.
                      </Form.Text>
                    </Form.Group>
                  </Col>
                </Row>

                {/* Opinion Document Upload */}
                <Row className="mb-4">
                  <Col md={12}>
                    <Form.Group>
                      <Form.Label>
                        <strong>بارگذاری صورتجلسه:</strong>
                      </Form.Label>
                      <Form.Control
                        type="file"
                        accept=".pdf,.doc,.docx"
                        onChange={handleFileUpload}
                        disabled={submitting}
                      />
                      <Form.Text className="text-muted">فرمت‌های مجاز: PDF, DOC, DOCX</Form.Text>

                      {formData.opinionDocument && (
                        <div className="mt-2">
                          <Alert variant="success" className="py-2">
                            <i className="fe fe-file me-2"></i>
                            فایل صورتجلسه با موفقیت آپلود شد: {formData.opinionDocument.name || 'فایل آپلود شده'}
                          </Alert>
                        </div>
                      )}
                    </Form.Group>
                  </Col>
                </Row>

                {/* Submit Button */}
                <div className="d-flex justify-content-end">
                  <Button type="submit" variant="success" disabled={submitting}>
                    {submitting ? (
                      <>
                        <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        در حال ثبت...
                      </>
                    ) : (
                      <>
                        <i className="fe fe-check me-2"></i>
                        ثبت نظریه و انتقال به مرحله بعد
                      </>
                    )}
                  </Button>
                </div>

                {/* Additional Information */}
                <Alert variant="info" className="mt-3">
                  <i className="fe fe-info me-2"></i>
                  <strong>نکته مهم:</strong> پس از تکمیل این بخش و اعلام نظریه به ذینفعان، مهلت دو هفته‌ای برای درخواست
                  تجدیدنظر فعال خواهد شد.
                </Alert>
              </Form>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Fragment>
  );
};

export default InitialOpinion;
