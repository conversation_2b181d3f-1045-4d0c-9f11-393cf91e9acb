import { Card, Form, FormGroup } from 'react-bootstrap';
import DatePicker from 'react-multi-date-picker';
import persian from 'react-date-object/calendars/persian';
import persian_fa from 'react-date-object/locales/persian_fa';
import Select from 'react-select';
import Provinces from '../../../utils/provinces.js';
import Cities from '../../../utils/cities.js';
import useSearchStore from '../../../store/searchStore.js';
import React, { Fragment } from 'react';
import { formatDate } from 'utils/helper.js';

const PersonFilters = ({ validationErrors }) => {
  const { filter, setFilter, removeFilter } = useSearchStore();

  return (
    <Fragment>
      <Card>
        <Card.Body className="p-2">
          <FormGroup className="form-group">
            <Form.Label htmlFor="fullname">نام گزارش‌شونده</Form.Label>
            <Form.Control
              id={'fullname'}
              type="text"
              size="sm"
              className="form-control"
              placeholder="نام و نام خانوادگی را وارد کنید"
              value={filter.q}
              onChange={(e) =>
                setFilter({
                  q: e.target.value,
                })
              }
              style={{ borderColor: validationErrors.q ? 'red' : 'auto' }}
            />
            <div className="invalid-feedback" style={{ display: validationErrors.q ? 'block' : 'none' }}>
              {validationErrors.q}
            </div>
          </FormGroup>
          <FormGroup className="form-group">
            <Form.Label htmlFor="fullname">نام گزارش‌دهنده</Form.Label>
            <Form.Control
              id={'fullname'}
              type="text"
              size="sm"
              className="form-control"
              placeholder="نام و نام خانوادگی را وارد کنید"
              value={filter.q}
              onChange={(e) =>
                setFilter({
                  q: e.target.value,
                })
              }
              style={{ borderColor: validationErrors.q ? 'red' : 'auto' }}
            />
            <div className="invalid-feedback" style={{ display: validationErrors.q ? 'block' : 'none' }}>
              {validationErrors.q}
            </div>
          </FormGroup>
          <FormGroup className="form-group">
            <Form.Label htmlFor="national_id">شماره پرونده</Form.Label>
            <Form.Control
              id={'national_id'}
              type="text"
              size="sm"
              className="form-control"
              placeholder="شماره پرونده را وارد کنید"
              value={filter.national_id}
              onChange={(e) =>
                setFilter({
                  national_id: e.target.value,
                })
              }
            />
            <div className="invalid-feedback" style={{ display: validationErrors.national_id ? 'block' : 'none' }}>
              {validationErrors.national_id}
            </div>
          </FormGroup>
          <FormGroup className="form-group pos-relative">
            <Form.Label htmlFor="birth_date">تاریخ ثبت پرونده</Form.Label>
            <DatePicker
              value={[
                filter.birth_date_from ? new Date(filter.birth_date_from) : null,
                filter.birth_date_to ? new Date(filter.birth_date_to) : null,
              ]}
              calendar={persian}
              locale={persian_fa}
              calendarPosition="bottom-right"
              containerClassName={'form-control'}
              range
              style={{
                background: 'transparent',
                width: '100%',
                boxShadow: 'none!important',
                outline: 'none',
                border: 'none',
              }}
              onChange={(e) => {
                const [start, end] = e;

                setFilter({
                  birth_date_from: start ? formatDate(new Date(start)) : null,
                  birth_date_to: end ? formatDate(new Date(end)) : null,
                });
              }}
            />
            <div
              className={'fe fe-x-circle'}
              style={{ position: 'absolute', cursor: 'pointer', top: '38px', left: '10px' }}
              onClick={() => {
                const { birth_date_from, birth_date_to, ...rest } = filter;
                removeFilter(rest);
              }}
            ></div>
          </FormGroup>

          <FormGroup className="form-group">
            <Form.Label htmlFor="birth_place">کارگروه رسیدگی کننده</Form.Label>
            <Select
              options={[
                { value: '', label: 'انتخاب کنید' },
                { value: '', label: 'کارگروه ۱' },
                { value: '', label: 'کارگروه ۲' },
                { value: '', label: 'کارگروه ۳' },
                { value: '', label: 'کارگروه ۴' },

              ]}
              placeholder="کارگروه را انتخاب کنید"
              classNamePrefix="Select2"
              isSearchable
              onChange={(e) =>
                setFilter({
                  person: {
                    ...filter.person,
                    birth_place: e.value,
                  },
                })
              }
            />
          </FormGroup>
        </Card.Body>
      </Card>
    </Fragment>
  );
};

export default PersonFilters;
