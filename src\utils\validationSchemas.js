import * as Yup from "yup";

export const loginSchema = Yup.object().shape({
  username: Yup.string().required("نام کاربری اجباری است"),
  password: Yup.string().required("رمز عبور اجباری است"),
});

export const forgetPasswordStep1Schema = Yup.object().shape({
  mobile: Yup.string().required("شماره موبایل اجباری است"),
});

export const forgetPasswordStep2Schema = Yup.object().shape({
  code: Yup.string().required("کد تایید اجباری است"),
});

export const forgetPasswordStep3Schema = Yup.object().shape({
  password: Yup.string().required("کلمه عبور جدید اجباری است"),
  repeatPassword: Yup.string().required("تکرار کلمه عبور اجباری است"),
});

export const basicSearchSchema = Yup.object().shape({
  q: Yup.string().required("کلمه برای جستجو اجباری است"),
});

export const customSearchSchema = Yup.object().shape({
  andWord: Yup.string(),
  orWord: Yup.string(),
  notWord: Yup.string(),
  q: Yup.string()
});

export const alertStepOneSchema = Yup.object().shape({
  title: Yup.string().required("عنوان اجباری است"),
});

export const alertStepTwoSchema = Yup.object().shape({
  messenger: Yup.string()
    .oneOf(["telegram", "bale", "e"])
    .required("انتخاب پیامرسان اجباری است"),
  channel_id: Yup.string().required("آیدی اجباری است"),
});

export const filterStepOneSchema = Yup.object().shape({
  title: Yup.string().required("عنوان اجباری است"),
});

export const updateProfileSchema = Yup.object().shape({
  phone_number: Yup.string()
    .length(11, "شماره موبایل وارد شده صحیح نمی‌باشد")
    .matches(/^09?(9\d{9})$/, "شماره موبایل وارد شده صحیح نمی‌باشد")
    .required("شماره موبایل اجباری است"),
  email: Yup.string()
    .min(5, "ایمیل وارد شده صحیح نمی‌باشد")
    .max(50, "ایمیل وارد شده صحیح نمی‌باشد")
    .matches(/^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/, "ایمیل وارد شده صحیح نمی‌باشد")
    .required("ایمیل اجباری است"),
  first_name: Yup.string()
    .min(2, "نام باید بیشتر از ۲ حرف باشد")
    .max(70, "نام باید کمتر از ۷۰ حرف باشد"),
  last_name: Yup.string()
    .min(2, "نام خانوادگی باید بیشتر از ۲ حرف باشد")
    .max(70, "نام خانوادگی باید کمتر از ۷۰ حرف باشد"),
  description: Yup.string()
    .min(2, "توضیحات باید بیشتر از ۲ حرف باشد")
    .max(255, "توضیحات باید کمتر از ۷۰ حرف باشد"),
});

export const bulletinStepOneSchema = Yup.object().shape({
  title: Yup.string().trim().required("عنوان اجباری است"),
});